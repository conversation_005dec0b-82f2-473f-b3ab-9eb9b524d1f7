import {
  defineConfig,
  minimal2023Preset,
  combinePresetAndAppleSplashScreens,
} from '@vite-pwa/assets-generator/config';

export default defineConfig({
  headLinkOptions: {
    preset: '2023',
  },
  preset: {
    ...minimal2023Preset,
    transparent: {
      sizes: [64, 192, 512],
      favicons: [[48, 'favicon.ico']],
      padding: 0.4,
      resizeOptions: {
        fit: 'contain',
        position: 'center',
        withoutEnlargement: false,
      },
    },
    maskable: {
      sizes: [512],
      padding: 0.3,
      resizeOptions: {
        fit: 'contain',
        position: 'center',
        withoutEnlargement: false,
      },
    },
    // Giữ nguyên cấu hình splash screen iOS
    appleSplashScreens: combinePresetAndAppleSplashScreens(minimal2023Preset, {
      padding: 0.6, // Giữ nguyên padding mặc định cho iOS
      resizeOptions: { background: '#f7f7f9', fit: 'contain' },
      darkResizeOptions: { background: '#0c0e13', fit: 'contain' },
      linkMediaOptions: {
        log: true,
        addMediaScreen: true,
        basePath: '/',
        xhtml: false,
      },
      png: {
        compressionLevel: 1,
        quality: 100,
        effort: 1,
      },
      name: (landscape, size, dark) => {
        return `apple-splash-${landscape ? 'landscape' : 'portrait'}-${typeof dark === 'boolean' ? (dark ? 'dark-' : 'light-') : ''}${size.width}x${size.height}.png`;
      },
    }).appleSplashScreens,
  },
  images: ['public/favicon.svg'],
});

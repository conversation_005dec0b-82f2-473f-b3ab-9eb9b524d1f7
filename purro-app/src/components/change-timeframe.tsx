import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import useMarketFilter from '@/store/marketFilter';
import { TimeFrame } from '@/types/geckoTerminal';

const ChangeTimeFrame = () => {
  const { timeFrame, setTimeFrame } = useMarketFilter();
  const handleChangeTimeFrame = (value: string) => {
    setTimeFrame(value as TimeFrame);
  };

  return (
    <ToggleGroup
      type="single"
      onValueChange={handleChangeTimeFrame}
      value={timeFrame}
      className="bg-card rounded-lg p-1 shadow-sm"
    >
      <ToggleGroupItem
        value="5m"
        aria-label="5m"
        className="data-[state=on]:bg-background data-[state=on]:text-primary hover:bg-background hover:text-primary rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 data-[state=on]:shadow-sm"
      >
        5m
      </ToggleGroupItem>
      <ToggleGroupItem
        value="1h"
        aria-label="1h"
        className="data-[state=on]:bg-background data-[state=on]:text-primary hover:bg-background hover:text-primary rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 data-[state=on]:shadow-sm"
      >
        1h
      </ToggleGroupItem>
      <ToggleGroupItem
        value="6h"
        aria-label="6h"
        className="data-[state=on]:bg-background data-[state=on]:text-primary hover:bg-background hover:text-primary rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 data-[state=on]:shadow-sm"
      >
        6h
      </ToggleGroupItem>
      <ToggleGroupItem
        value="24h"
        aria-label="24h"
        className="data-[state=on]:bg-background data-[state=on]:text-primary hover:bg-background hover:text-primary rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 data-[state=on]:shadow-sm"
      >
        24h
      </ToggleGroupItem>
    </ToggleGroup>
  );
};

export default ChangeTimeFrame;

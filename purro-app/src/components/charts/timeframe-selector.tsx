import React from "react";
import { cn } from "@/lib/utils";

// Timeframe selector component
interface TimeframeSelectorProps {
    timeFrame: '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all';
    onChange: (timeFrame: '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all') => void;
}

const TimeframeSelector: React.FC<TimeframeSelectorProps> = ({ timeFrame, onChange }) => {
    const timeframes = [
        { value: '1h', label: '1H' },
        { value: '24h', label: '1D' },
        { value: '1w', label: '1W' },
        { value: '1m', label: '1M' },
        { value: 'ytd', label: 'YTD' },
        { value: 'all', label: 'ALL' }
    ];

    return (
        <div className="flex items-center">
            {timeframes.map((tf) => (
                <button
                    key={tf.value}
                    className={cn("px-2 py-1 text-sm font-medium rounded", {
                        "bg-primary/10 text-primary": timeFrame === tf.value,
                        "hover:bg-muted": timeFrame !== tf.value,
                    })}
                    onClick={() => onChange(tf.value as '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all')}
                >
                    {tf.label}
                </button>
            ))}
        </div>
    );
};

export default TimeframeSelector;
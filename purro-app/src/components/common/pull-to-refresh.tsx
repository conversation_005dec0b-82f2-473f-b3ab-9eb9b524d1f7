import { useEffect, useState } from 'react';

export function PullToRefresh() {
  const [startY, setStartY] = useState<number | null>(null);
  const [pullDistance, setPullDistance] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const threshold = 150; // Khoảng cách kéo cần thiết để kích hoạt làm mới

  useEffect(() => {
    const handleTouchStart = (e: TouchEvent) => {
      // Chỉ bắt đầu theo dõi khi người dùng ở đầu trang
      if (window.scrollY === 0) {
        const touchY = e.touches[0].clientY;
        // Chỉ kích hoạt khi chạm vào phần trên cùng của màn hình (ví dụ: 100px đầu tiên)
        const topThreshold = 100; // C<PERSON> thể điều chỉnh giá trị này

        if (touchY <= topThreshold) {
          setStartY(e.touches[0].clientY);
        } else {
          setStartY(null);
        }
      } else {
        // Đảm bảo startY là null nếu không ở đầu trang
        setStartY(null);
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      // Nếu không có điểm bắt đầu, hoặc không ở đầu trang, không xử lý
      if (startY === null || window.scrollY > 0) return;

      const currentY = e.touches[0].clientY;
      const diff = currentY - startY;

      // Chỉ xử lý kéo xuống (diff > 0)
      if (diff > 0) {
        // Ngăn chặn hành vi cuộn mặc định chỉ khi đang thực hiện pull-to-refresh
        e.preventDefault();

        // Giới hạn khoảng cách kéo tối đa và áp dụng hiệu ứng giảm tốc
        const resistance = 0.5; // Yếu tố kháng cự để làm chậm hiệu ứng kéo
        const newPullDistance = Math.min(threshold * 1.5, diff * resistance);
        setPullDistance(newPullDistance);
      }
    };

    const handleTouchEnd = () => {
      // Chỉ xử lý khi có điểm bắt đầu (tức là đã bắt đầu kéo từ đầu trang)
      if (startY === null) return;

      // Nếu kéo đủ xa, kích hoạt làm mới
      if (pullDistance > threshold) {
        setRefreshing(true);
        // Tải lại trang sau một chút hiệu ứng
        setTimeout(() => {
          window.location.reload();
        }, 500);
      }

      // Reset khoảng cách kéo với hiệu ứng mượt
      setPullDistance(0);
      setStartY(null);
    };

    document.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [startY, pullDistance, threshold]);

  return (
    <div className="relative">
      {/* Indicator khi người dùng kéo */}
      <div
        className="fixed top-0 left-0 z-50 flex w-full items-center justify-center overflow-hidden transition-opacity"
        style={{
          height: `${pullDistance}px`,
          opacity: pullDistance > 0 ? 1 : 0,
          transition: startY === null ? 'height 0.3s ease-out' : 'none',
        }}
      >
        <div className="flex flex-col items-center justify-center">
          <svg
            className={`h-6 w-6 transition-transform ${pullDistance > threshold ? 'rotate-180' : ''
              }`}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
          <span className="mt-1 text-sm">
            {pullDistance > threshold ? 'Release to refresh' : 'Pull to refresh'}
          </span>
        </div>
      </div>

      {/* Thanh hiển thị khi đang làm mới */}
      {refreshing && (
        <div className="bg-primary fixed top-0 left-0 z-50 h-1 w-full">
          <div className="bg-primary h-full w-full animate-[progress_1s_ease-in-out_infinite]"></div>
        </div>
      )}
    </div>
  );
}

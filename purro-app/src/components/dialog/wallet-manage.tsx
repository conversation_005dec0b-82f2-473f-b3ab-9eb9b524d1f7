import { truncateAddress, cn } from '@/lib/utils';
import { ConnectedWallet, useWallets } from '@privy-io/react-auth';
import { useSetActiveWallet } from '@privy-io/wagmi';
import { useAccount } from 'wagmi';
import { usePrivy } from '@privy-io/react-auth';
import { Button } from '../ui/button';
import { ChevronDownIcon, Copy, FileKey, Plus, Wallet2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

const WalletManage = () => {
    const { address } = useAccount();
    const { wallets } = useWallets();
    const { setActiveWallet } = useSetActiveWallet();
    const { connectWallet } = usePrivy();
    const embeddedWallets = wallets.filter((wallet) => wallet.connectorType === 'embedded');
    const injectedWallets = wallets.filter((wallet) => wallet.connectorType === 'injected');
    const importedWallets = wallets.filter((wallet) => wallet.connectorType === 'embedded_imported');

    const handleSetActiveWallet = (wallet: ConnectedWallet) => {
        try {
            setActiveWallet(wallet);
        } catch (error) {
            console.log(error);
        }
    }

    return (
        <div className="flex flex-col h-full max-h-[80vh] custom-scrollbar">
            <div className="flex-shrink-0 p-4 md:p-0 !pb-2 sticky top-0 z-10 border-b flex items-center justify-between">
                <h2 className="text-xl font-semibold">
                    Wallets
                </h2>
                <Button onClick={() => connectWallet()} className="text-sm"><Plus /> Link / Add</Button>
            </div>
            <div className="p-4 md:p-0 pb-safe overflow-y-auto md:mt-2">
                {
                    embeddedWallets.length > 0 && (
                        <>
                            <p className="text-sm text-muted-foreground">Embedded</p>
                            <div className="mb-2 space-y-2">
                                {embeddedWallets?.map((wallet) => (
                                    <WalletItem key={wallet.address + wallet.walletIndex} wallet={wallet} handleSetActiveWallet={handleSetActiveWallet} currentAddress={address || ''} />
                                ))}
                            </div>
                        </>
                    )
                }
                {
                    importedWallets.length > 0 && (
                        <>
                            <p className="text-sm text-muted-foreground">Imported</p>
                            <div className="mb-2 space-y-2">
                                {importedWallets?.map((wallet) => (<>
                                    <WalletItem key={wallet.address + wallet.walletIndex} wallet={wallet} handleSetActiveWallet={handleSetActiveWallet} currentAddress={address || ''} />
                                </>))}
                            </div>
                        </>
                    )
                }
                {
                    injectedWallets.length > 0 && (
                        <>
                            <p className="text-sm text-muted-foreground">Connected</p>
                            <div className="mb-2 space-y-2">
                                {injectedWallets?.map((wallet) => (<>
                                    <WalletItem key={wallet.address + wallet.walletIndex} wallet={wallet} handleSetActiveWallet={handleSetActiveWallet} currentAddress={address || ''} />
                                </>))}
                            </div>
                        </>
                    )
                }
            </div>
        </div>
    )
}

const WalletItem = ({ wallet, handleSetActiveWallet, currentAddress }: { wallet: ConnectedWallet, handleSetActiveWallet: (wallet: ConnectedWallet) => void, currentAddress: string }) => {
    const [openSetting, setOpenSetting] = useState(false);
    const isActive = wallet.address === currentAddress;
    const { exportWallet } = usePrivy();
    const isCanExportKey = wallet.connectorType === 'embedded' || wallet.connectorType === 'embedded_imported';

    const handleExportWallet = async () => {
        try {
            await exportWallet({
                address: wallet.address
            });
        } catch {
            toast.error("Export wallet failed");
        }
    }

    return (
        <div className={cn(
            "bg-card rounded-2xl transition-all duration-300 ease-in-out border-2",
            isActive
                ? "bg-primary/10 border-primary shadow-lg shadow-primary/20"
                : "bg-card border-border hover:border-primary/30 hover:shadow-md"
        )}>
            {/* Header - Wallet Info */}
            <div
                key={wallet.address + wallet.walletIndex}
                className={cn(
                    "flex items-center justify-between gap-3 pl-4 cursor-pointer group",
                    "hover:bg-muted/50 transition-colors duration-200",
                    openSetting ? "rounded-t-2xl" : "rounded-2xl"
                )}
                onClick={() => setOpenSetting(!openSetting)}
            >
                <div className="flex items-center gap-3 w-full py-4 pr-4">
                    {/* Wallet Icon */}
                    <div>
                        {wallet.meta.icon ? <img src={wallet.meta.icon} alt={wallet.meta.name} className="size-5" /> : <Wallet2 className="size-5" />}
                    </div>

                    {/* Wallet Address */}
                    <div className="flex-1">
                        <p className="text-base font-medium">
                            {truncateAddress(wallet.address)}
                        </p>
                        {isActive && (
                            <p className="text-xs text-primary font-medium mt-1">
                                Active Wallet
                            </p>
                        )}
                    </div>

                    {/* Chevron Icon */}
                    <div className={cn(
                        "transition-transform duration-300 ease-in-out",
                        openSetting ? "rotate-180" : "rotate-0"
                    )}>
                        <ChevronDownIcon className="size-5 text-muted-foreground group-hover:text-foreground" />
                    </div>
                </div>
            </div>

            {/* Settings Panel with Animation */}
            <div className={cn(
                "overflow-hidden transition-all duration-300 ease-in-out",
                openSetting ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
            )}>
                <div className="border-t border-border bg-muted/30">
                    <div className="p-4 space-y-3">
                        {/* Action Buttons */}
                        <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                            {!isActive && (
                                <Button
                                    onClick={() => handleSetActiveWallet(wallet)}
                                    className="w-full justify-center gap-2 bg-primary hover:bg-primary/90"
                                    size="sm"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Set as Active
                                </Button>
                            )}

                            <Button
                                variant="outline"
                                size="sm"
                                className="w-full justify-center gap-2 hover:bg-muted"
                                onClick={() => {
                                    navigator.clipboard.writeText(wallet.address);
                                    toast.success("Copied address");
                                }}
                            >
                                <Copy />
                                Copy Address
                            </Button>

                            {isCanExportKey && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full justify-center gap-2 hover:bg-muted"
                                    onClick={handleExportWallet}
                                >
                                    <FileKey />
                                    Export Key
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WalletManage
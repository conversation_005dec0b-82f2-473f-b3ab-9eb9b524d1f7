import useDynamicDrawerStore from '@/store/dynamicDrawer';
import { DynamicEmbeddedUserProfile } from '@dynamic-labs/sdk-react-core';
import React, { useState, useRef, useEffect, TouchEvent } from 'react';

interface DynamicDrawerProps {
  contentHeight?: number;
  dragOnlyHandle?: boolean; // Thêm prop để điều khiển hành vi kéo
}

const DynamicDrawer: React.FC<DynamicDrawerProps> = ({
  contentHeight = 400,
  dragOnlyHandle = false, // Mặc định cho phép kéo trên toàn drawer
}) => {
  const { isOpen, setIsOpen } = useDynamicDrawerStore();
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const drawerRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleTouchStart = (e: TouchEvent<HTMLDivElement>): void => {
    // Nếu dragOnlyHandle = true, chỉ cho phép kéo từ thanh kéo
    // Nếu dragOnlyHandle = false, cho phép kéo từ mọi nơi trên drawer
    if (dragOnlyHandle) {
      // Kiểm tra xem touch có bắt đầu từ thanh kéo không
      if (handleRef.current && handleRef.current.contains(e.target as Node)) {
        setStartY(e.touches[0].clientY);
        setIsDragging(true);
      }
    } else {
      // Cho phép kéo từ bất kỳ đâu trên drawer
      setStartY(e.touches[0].clientY);
      setIsDragging(true);
    }
  };

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>): void => {
    if (!isDragging) return;

    const currentTouchY = e.touches[0].clientY;
    const diff = currentTouchY - startY;

    // Chỉ cho phép kéo xuống, không kéo lên quá vị trí ban đầu
    if (diff < 0) {
      setCurrentY(0);
    } else {
      setCurrentY(diff);
    }
  };

  const handleTouchEnd = (): void => {
    if (!isDragging) return;

    setIsDragging(false);

    // Nếu kéo xuống quá 1/3 chiều cao, đóng drawer
    if (currentY > contentHeight / 3) {
      handleClose();
    } else {
      // Nếu không, trả về vị trí ban đầu
      setCurrentY(0);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setCurrentY(0);
    }
  }, [isOpen]);

  const getDrawerStyle = (): React.CSSProperties => {
    const translateY = isOpen ? `translateY(${currentY}px)` : 'translateY(100%)';
    return {
      transform: translateY,
      transition: isDragging ? 'none' : 'transform 0.3s ease-out',
    };
  };

  return (
    <>
      {/* Overlay nền */}
      {isOpen && (
        <div
          className="fixed top-0 right-0 bottom-0 left-0 z-50 bg-black/40 transition-opacity duration-300"
          onClick={handleClose}
        />
      )}

      {/* Drawer */}
      <div
        ref={drawerRef}
        className="bg-background fixed right-0 bottom-0 left-0 z-[9999] rounded-t-2xl shadow-lg"
        style={getDrawerStyle()}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Thanh kéo - thêm visual cue nếu chỉ có thể kéo từ đây */}
        <div
          ref={handleRef}
          className={`flex justify-center pt-2 pb-2 ${dragOnlyHandle ? 'cursor-grab active:cursor-grabbing' : ''}`}
        >
          <div className={`bg-card h-1 w-12 rounded-full`}></div>
        </div>

        <DynamicEmbeddedUserProfile />
      </div>
    </>
  );
};

export default DynamicDrawer;

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '../ui/sidebar';
import { NavSecondary } from './app-sidebar-nav-secondary';
import { NavMain } from './app-sidebar-nav-main';
import { navItems, navSecondaryItems } from './navItems';

const data = {
  navMain: navItems,
  navSecondary: navSecondaryItems,
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5 mb-4">
              <a href="/">
                <img src="/icon-transparent.png" alt="Purro" className="size-8 rounded" />
                <span className="text-xl font-semibold">Purro</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
    </Sidebar>
  );
}

import { CandlestickChartIcon, GroupedBarChartIcon, SwapHorizontalCircleIcon, WalletIcon } from '@/assets/icons';
import RewardedAds from '@/assets/icons/RewardedAds';
import { Book, LucideIcon, SettingsIcon } from 'lucide-react';
import React from 'react';

type NavItem = {
  title: string;
  url: string;
  icon: LucideIcon | React.FC;
};

export const navItems: NavItem[] = [
  {
    title: 'Wallet',
    url: '/',
    icon: WalletIcon,
  },
  {
    title: 'Market',
    url: '/market',
    icon: GroupedBarChartIcon,
  },
  {
    title: 'Trade',
    url: '/trade',
    icon: SwapHorizontalCircleIcon,
  },
  {
    title: "Futures",
    url: "/futures",
    icon: CandlestickChartIcon,
  },
  // {
  //     name: "Earn",
  //     href: "/earn",
  //     icon: Gem,
  // },
  // {
  //   title: 'Explore',
  //   url: '/explore',
  //   icon: SearchInsideIcon,
  // },
  {
    title: 'Campaign',
    url: '/campaign',
    icon: RewardedAds,
  },
];

export const navSecondaryItems: NavItem[] = [
  {
    title: 'Settings',
    url: '/settings',
    icon: SettingsIcon,
  },
  // {
  //   title: 'Get Help',
  //   url: '#',
  //   icon: HelpCircleIcon,
  // },
  {
    title: 'Documentation',
    url: 'https://docs.purro.xyz',
    icon: Book,
  }
];


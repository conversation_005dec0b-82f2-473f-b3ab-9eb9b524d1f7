import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { Link, useLocation, useNavigate } from 'react-router';
import { Button } from '../ui/button';
import { ChevronDownIcon, SettingsIcon } from 'lucide-react';
import { useMediaQuery } from 'react-responsive';
import useHyperliquidSocketStore from '@/store/hyperliquidSocket';
import HyperliquidLogo from '@/assets/logos/hyperliquid_logo.svg';
import { usePrivy } from '@privy-io/react-auth';
import { truncateAddress } from '@/lib/utils';
import useResponsiveDialogStore from '@/store/responsiveDialog';
import WalletManage from '../dialog/wallet-manage';

export function SiteHeader() {
  const { pathname } = useLocation();
  const isWalletPage = isWallet(pathname);
  const pageTitle = getPageTitle(pathname);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });
  const { status: connection } = useHyperliquidSocketStore();
  const { user, authenticated } = usePrivy();
  const { setIsOpen, setComponent } = useResponsiveDialogStore();
  const navigate = useNavigate();

  const handleWalletManage = () => {
    setComponent(<WalletManage />);
    setIsOpen(true);
  };

  return (
    <header
      className={cn(
        'z-10 flex h-12 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12',
        isWalletPage && 'bg-primary text-primary-foreground'
      )}
    >
      <div className="flex w-full items-center justify-between gap-1 px-4 lg:gap-2 lg:px-6">
        <div className="flex items-center gap-2">
          <div className="hidden items-center md:flex">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />
          </div>
          <h1 className="hidden text-lg font-medium md:block">{pageTitle}</h1>

          <div className="block md:hidden">
            {authenticated ? (
              <Button
                variant="ghost"
                className="cursor-pointer text-base"
                onClick={handleWalletManage}
              >
                {truncateAddress(user?.wallet?.address || '')}
                <ChevronDownIcon className="size-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                className="cursor-pointer text-base"
                onClick={() => navigate('/login')}
              >
                Connect
              </Button>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {/* <div className="hover:bg-background hover:text-primary cursor-pointer rounded-full p-2 transition-colors duration-200 ease-linear">
            <BellIcon className="size-5" />
          </div>
          <div className="hover:bg-background hover:text-primary cursor-pointer rounded-full p-2 transition-colors duration-200 ease-linear">
            <SearchIcon className="size-5" strokeWidth={2} />
          </div> */}
          <div className="mr-2 hidden md:block">
            {authenticated ? (
              <Button
                variant="ghost"
                className="cursor-pointer text-base"
                onClick={handleWalletManage}
              >
                {truncateAddress(user?.wallet?.address || '')}
                <ChevronDownIcon className="size-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                className="cursor-pointer text-base"
                onClick={() => navigate('/login')}
              >
                Connect
              </Button>
            )}
          </div>

          {/* {user && (
            <Button
              variant="ghost"
              className="hidden items-center gap-1 px-2 text-left md:flex md:px-3"
              onClick={() => setShowDynamicUserProfile(true)}
            >
              <span className="text-lg font-semibold">{`${(user.metadata as Record<string, string>)?.['name-service-subdomain-handle'] || ''}.purro.eth`}</span>
              <ChevronDownIcon className="size-4" />
            </Button>
          )} */}
          <Link
            to={isMobile ? '/mobile-settings' : '/settings'}
            className="hover:bg-background hover:text-primary cursor-pointer rounded-full p-2 transition-colors duration-200 ease-linear md:hidden"
          >
            <SettingsIcon className="size-5" strokeWidth={2} />
          </Link>
          <div className="relative ml-2 w-8 md:ml-0">
            <img src={HyperliquidLogo} alt="" className="size-6" />

            {connection === 'connected' ? (
              <>
                <div className="absolute right-2 bottom-0 h-2 w-2 animate-ping rounded-full bg-green-500" />
                <div className="absolute right-2 bottom-0 h-2 w-2 rounded-full bg-green-500" />
              </>
            ) : (
              <>
                <div className="absolute right-2 bottom-0 h-2 w-2 rounded-full bg-red-500" />
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

const isWallet = (pathname: string) => pathname === '/' || pathname.startsWith('/wallet');

const getPageTitle = (pathname: string) => {
  if (pathname === '/' || pathname.startsWith('/wallet')) {
    return 'Wallet';
  } else if (pathname.startsWith('/market')) {
    return 'Market';
  } else if (pathname.startsWith('/trade')) {
    return 'Trade';
  } else if (pathname.startsWith('/explore')) {
    return 'Explore';
  } else if (pathname.startsWith('/settings')) {
    return 'Settings';
  } else if (pathname.startsWith('/futures')) {
    return 'Futures';
  } else if (pathname.startsWith('/campaign')) {
    return 'Campaign';
  } else {
    return 'Documents';
  }
};

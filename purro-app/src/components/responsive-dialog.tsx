import useResponsiveDialogStore from "@/store/responsiveDialog";
import {
    Dialog,
    DialogHeader,
    DialogTitle,
    DialogContent,
    DialogDescription
} from "./ui/dialog";
import { useMediaQuery } from "react-responsive";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { Drawer, DrawerContent } from "./ui/drawer";

const ResponsiveDialog = () => {
    const { isOpen, component, setIsOpen } = useResponsiveDialogStore();
    const isMobile = useMediaQuery({ query: "(max-width: 768px)" })

    if (!isMobile) {
        return (
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent>
                    <VisuallyHidden>
                        <DialogHeader>
                            <DialogTitle>Dialog</DialogTitle>
                            <DialogDescription>
                                Dialog
                            </DialogDescription>
                        </DialogHeader>
                    </VisuallyHidden>
                    {component}
                </DialogContent>
            </Dialog>
        )
    }

    return (
        <Drawer open={isOpen} onOpenChange={setIsOpen}>
            <DrawerContent>
                <VisuallyHidden>
                    <DialogHeader>
                        <DialogTitle>Dialog</DialogTitle>
                        <DialogDescription>
                            Dialog
                        </DialogDescription>
                    </DialogHeader>
                </VisuallyHidden>
                {component}
            </DrawerContent>
        </Drawer>
    )
}

export default ResponsiveDialog
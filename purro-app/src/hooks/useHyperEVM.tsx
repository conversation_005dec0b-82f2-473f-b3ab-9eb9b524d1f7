import { useQuery } from '@tanstack/react-query';
import { fetchHyperEvmERC20Tokens, fetchHyperEvmNfts, fetchHyperEvmNftsCollection } from '../services/hyperscanApi';
import { fetchHyperEvmTokenPrices } from '@/services/geckoTerminalApi';
import { HyperScanNftNextPageParams } from '@/types/hyperEvm';
import QueryKeys from '@/constants/queryKeys';

const useHyperEVM = () => {
    const useERC20Tokens = (address: string) => {
        return useQuery({
            queryKey: [QueryKeys.HYPER_EVM_ERC20_TOKENS, address],
            queryFn: () => fetchHyperEvmERC20Tokens(address),
            staleTime: 30 * 1000, // 30 seconds
        });
    };

    const useHyperEVMTokenPrices = (addresses: string[], options = {}) => {
        return useQuery({
            queryKey: [QueryKeys.HYPER_EVM_TOKEN_PRICES, addresses],
            queryFn: () => fetchHyperEvmTokenPrices(addresses),
            enabled: !!addresses.length,
            staleTime: 30 * 1000, // 3 seconds
            ...options,
        });
    };

    const useNFTsCollections = (address: string) => {
        return useQuery({
            queryKey: [QueryKeys.HYPER_EVM_NFTS_COLLECTIONS, address],
            queryFn: () => fetchHyperEvmNftsCollection(address),
            staleTime: 30 * 1000, // 30 seconds
        });
    };

    const useNFTs = (address: string, nextPageParams?:
        HyperScanNftNextPageParams
    ) => {
        return useQuery({
            queryKey: [QueryKeys.HYPER_EVM_NFTS, address, nextPageParams],
            queryFn: () => fetchHyperEvmNfts(address, nextPageParams),
            staleTime: 60 * 1000 * 10, // 10 minutes
        })
    };

    return {
        useERC20Tokens,
        useHyperEVMTokenPrices,
        useNFTsCollections,
        useNFTs,
    };
};

export default useHyperEVM;


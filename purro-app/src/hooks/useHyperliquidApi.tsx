import { useQuery } from '@tanstack/react-query';
import {
  fetchUserSpotBalance,
  fetchUserPerpsBalance,
  fetchSpotAssetsContext,
  fetchSpotTokenDetails,
  fetchHLCandleSnapshot,
} from '@/services/hyperliquidApi';
import QueryKeys from '@/constants/queryKeys';

const useHyperliquidApi = () => {
  const useSpotAssetsContext = () => {
    return useQuery({
      queryKey: [QueryKeys.SPOT_ASSETS],
      queryFn: () => fetchSpotAssetsContext(),
      staleTime: 30 * 1000, // 3 seconds
    });
  };

  const useUserSpotBalance = (address: string, options = {}) => {
    return useQuery({
      queryKey: [QueryKeys.SPOT_USER_BALANCE, address],
      queryFn: () => fetchUserSpotBalance(address),
      enabled: !!address,
      staleTime: 30 * 1000, // 3 seconds
      ...options,
    });
  };

  const useUserPerpsBalance = (address: string, options = {}) => {
    return useQuery({
      queryKey: [QueryKeys.PERPS_USER_BALANCE, address],
      queryFn: () => fetchUserPerpsBalance(address),
      enabled: !!address,
      staleTime: 30 * 1000, // 3 seconds
      ...options,
    });
  };

  const useSpotTokenDetails = (tokenId: string, options = {}) => {
    return useQuery({
      queryKey: [QueryKeys.SPOT_TOKEN_DETAILS, tokenId],
      queryFn: () => fetchSpotTokenDetails(tokenId),
      enabled: !!tokenId,
      staleTime: 1 * 1000, // 1 seconds
      refetchInterval: 1 * 1000, // 1 second
      ...options,
    });
  };

  const useCandleSnapshot = (coinId: string, timeFrame: '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all', tokenLaunchDate?: number, options = {}) => {
    return useQuery({
      queryKey: [QueryKeys.SPOT_CANDLE_SNAPSHOT, coinId, timeFrame, tokenLaunchDate],
      queryFn: () => fetchHLCandleSnapshot(coinId, timeFrame, tokenLaunchDate),
      enabled: !!coinId,
      staleTime: 30 * 1000, // 3 seconds
      ...options,
    });
  }


  return {
    useSpotAssetsContext,
    useUserSpotBalance,
    useUserPerpsBalance,
    useSpotTokenDetails,
    useCandleSnapshot,
  };
};

export default useHyperliquidApi;

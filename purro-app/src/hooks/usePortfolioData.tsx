import { useQueries } from '@tanstack/react-query';
import HyperLiquidSpotDataIndexer from '@/classes/HyperliquidSpotDataIndexer';
import { useMemo } from 'react';
import {
  fetchSpotAssetsContext,
  fetchUserPerpsBalance,
  fetchUserSpotBalance,
} from '@/services/hyperliquidApi';
import { HyperliquidApiSpotAssetContext } from '@/types/hyperliquidSpot';
import { HyperScanTokenBalanceResponse } from '@/types/hyperEvm';
import { fetchHyperEvmTokenPrices } from '@/services/geckoTerminalApi';
import { fetchHyperEvmERC20Tokens } from '@/services/hyperscanApi';
import QueryKeys from '@/constants/queryKeys';

// Thêm interface cho options
interface PortfolioOptions {
  fetchSpot?: boolean;
  fetchPerps?: boolean;
  fetchEvm?: boolean;
}

export const usePortfolioData = (
  userAddress: string,
  options: PortfolioOptions = {
    fetchSpot: true,
    fetchPerps: true,
    fetchEvm: true,
  }
) => {
  const { fetchSpot = true, fetchPerps = true, fetchEvm = true } = options;

  // Xây dựng mảng queries dựa trên options
  const queries = [];

  // Thêm spot queries nếu cần
  if (fetchSpot) {
    queries.push(
      {
        queryKey: [QueryKeys.SPOT_USER_BALANCE, userAddress],
        queryFn: () => fetchUserSpotBalance(userAddress),
        staleTime: 30 * 1000, // 3 seconds
      },
      {
        queryKey: [QueryKeys.SPOT_ASSETS],
        queryFn: () => fetchSpotAssetsContext(),
        staleTime: 30 * 1000, // 3 seconds
      }
    );
  }

  // Thêm perps query nếu cần
  if (fetchPerps) {
    queries.push({
      queryKey: [QueryKeys.PERPS_USER_BALANCE, userAddress],
      queryFn: () => fetchUserPerpsBalance(userAddress),
      staleTime: 30 * 1000, // 3 seconds
    });
  }

  // Thêm evm tokens query nếu cần
  if (fetchEvm) {
    queries.push({
      queryKey: [QueryKeys.HYPER_EVM_ERC20_TOKENS, userAddress],
      queryFn: () => fetchHyperEvmERC20Tokens(userAddress),
      staleTime: 30 * 1000, // 3 seconds
    });
  }

  // Sử dụng React Query để fetch dữ liệu
  const results = useQueries({
    queries,
  });

  // Xử lý kết quả và gán vào các biến tương ứng
  let indexResults = 0;

  // Mặc định các query với trạng thái không fetch
  const defaultQuery = {
    data: null,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  };

  // Gán kết quả hoặc giá trị mặc định dựa trên options
  const spotBalanceQuery = fetchSpot ? results[indexResults++] : defaultQuery;
  const spotContextQuery = fetchSpot ? results[indexResults++] : defaultQuery;
  const perpsBalanceQuery = fetchPerps ? results[indexResults++] : defaultQuery;
  const evmTokensQuery = fetchEvm ? results[indexResults++] : defaultQuery;

  // Process spot data
  const spotIndexer = useMemo(() => {
    if (!fetchSpot) return null;

    const contextData = spotContextQuery.data;
    if (!contextData || !Array.isArray(contextData) || contextData.length < 2) {
      return null;
    }
    try {
      return new HyperLiquidSpotDataIndexer(contextData as HyperliquidApiSpotAssetContext);
    } catch (error) {
      console.error('Error creating SpotDataIndexer:', error);
      return null;
    }
  }, [fetchSpot, spotContextQuery.data]);

  const userSpotBalances = useMemo(() => {
    if (!fetchSpot) return [];

    const balanceData = spotBalanceQuery.data;
    if (!spotIndexer || !balanceData) {
      return [];
    }
    return spotIndexer.processUserBalances(balanceData);
  }, [fetchSpot, spotIndexer, spotBalanceQuery.data]);

  const spotValue = useMemo(() => {
    if (!fetchSpot || !userSpotBalances.length) return 0;
    return spotIndexer?.getPortfolioValue(userSpotBalances) || 0;
  }, [fetchSpot, userSpotBalances, spotIndexer]);

  // Fetch token prices if we have evmTokens
  const tokenAddresses = useMemo(() => {
    if (!fetchEvm) return [];
    return (
      evmTokensQuery.data?.items?.map(
        (item: HyperScanTokenBalanceResponse) => item.token.address
      ) || []
    );
  }, [fetchEvm, evmTokensQuery.data]);

  const tokenPricesQuery = useQueries({
    queries: [
      {
        queryKey: [QueryKeys.HYPER_EVM_TOKEN_PRICES, tokenAddresses],
        queryFn: () =>
          tokenAddresses.length > 0
            ? fetchHyperEvmTokenPrices(tokenAddresses)
            : Promise.resolve(null),
        staleTime: 30 * 1000, // 3 seconds
        enabled: fetchEvm && tokenAddresses.length > 0, // Chỉ run nếu fetchEvm=true và có token addresses
      },
    ],
  })[0];

  // Calculate EVM value
  const tokenPricesData = useMemo(() => {
    if (!fetchEvm) return {};
    return (
      (!tokenPricesQuery.isLoading && tokenPricesQuery.data?.data?.attributes?.token_prices) || {}
    );
  }, [fetchEvm, tokenPricesQuery.data, tokenPricesQuery.isLoading]);

  const evmValue = useMemo(() => {
    if (!fetchEvm || !evmTokensQuery.data?.items || tokenPricesQuery.isLoading) return 0;

    return evmTokensQuery.data.items.reduce(
      (total: number, item: HyperScanTokenBalanceResponse) => {
        const value = parseFloat(item.value) / Math.pow(10, parseFloat(item.token.decimals));
        const tokenAddress = item.token.address.toLowerCase();
        const price =
          tokenPricesData && tokenAddress in tokenPricesData
            ? parseFloat(tokenPricesData[tokenAddress])
            : 0;
        return total + value * price;
      },
      0
    );
  }, [fetchEvm, evmTokensQuery.data, tokenPricesData, tokenPricesQuery.isLoading]);

  // Calculate perps value
  const perpsValue = useMemo(() => {
    if (!fetchPerps || !perpsBalanceQuery.data?.marginSummary?.accountValue) return 0;
    return parseFloat(perpsBalanceQuery.data.marginSummary.accountValue);
  }, [fetchPerps, perpsBalanceQuery.data]);

  // Calculate total portfolio value
  const totalValue = useMemo(() => {
    return spotValue + perpsValue + evmValue;
  }, [spotValue, perpsValue, evmValue]);

  // Check loading and error states
  const isSpotLoading = fetchSpot && (spotBalanceQuery.isLoading || spotContextQuery.isLoading);
  const isPerpsLoading = fetchPerps && perpsBalanceQuery.isLoading;
  const isEvmLoading =
    fetchEvm &&
    (evmTokensQuery.isLoading ||
      (fetchEvm && tokenAddresses.length > 0 && tokenPricesQuery.isLoading));

  const spotError = fetchSpot && (spotBalanceQuery.error || spotContextQuery.error);
  const perpsError = fetchPerps && perpsBalanceQuery.error;
  const evmError = fetchEvm && (evmTokensQuery.error || tokenPricesQuery.error);

  // Combine all data for easy access
  return {
    // Portfolio values
    totalValue,
    spotValue,
    perpsValue,
    evmValue,

    // Loading states
    isSpotLoading,
    isPerpsLoading,
    isEvmLoading,
    isLoading: isSpotLoading || isPerpsLoading || isEvmLoading,

    // Error states
    spotError,
    perpsError,
    evmError,
    hasError: !!spotError || !!perpsError || !!evmError,

    // Raw data
    spotData: fetchSpot
      ? {
        balanceData: spotBalanceQuery.data,
        contextData: spotContextQuery.data,
        userBalances: userSpotBalances,
        indexer: spotIndexer,
      }
      : null,
    perpsData: fetchPerps ? perpsBalanceQuery.data : null,
    evmData: fetchEvm
      ? {
        tokensData: evmTokensQuery.data,
        tokenPricesData,
      }
      : null,

    // Refetch functions
    refetchAll: () => {
      const promises = [];
      if (fetchSpot) {
        promises.push(spotBalanceQuery.refetch());
        promises.push(spotContextQuery.refetch());
      }
      if (fetchPerps) {
        promises.push(perpsBalanceQuery.refetch());
      }
      if (fetchEvm) {
        promises.push(evmTokensQuery.refetch());
        if (tokenAddresses.length > 0) {
          promises.push(tokenPricesQuery.refetch());
        }
      }
      return Promise.all(promises);
    },
    refetchSpot: () => {
      if (!fetchSpot) return Promise.resolve();
      return Promise.all([spotBalanceQuery.refetch(), spotContextQuery.refetch()]);
    },
    refetchPerps: () => {
      if (!fetchPerps) return Promise.resolve();
      return perpsBalanceQuery.refetch();
    },
    refetchEvm: async () => {
      if (!fetchEvm) return Promise.resolve();
      const evmPromise = evmTokensQuery.refetch();
      if (tokenAddresses.length > 0) {
        return evmPromise.then(() => tokenPricesQuery.refetch());
      }
      return evmPromise;
    },
  };
};

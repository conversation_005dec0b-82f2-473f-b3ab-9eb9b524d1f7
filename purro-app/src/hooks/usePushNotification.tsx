import { ENDPOINTS } from '@/services/endpoints';
import { useState, useEffect } from 'react';

// Interface definitions
interface PushSubscription {
  endpoint: string;
  expirationTime: number | null;
  keys: {
    p256dh: string;
    auth: string;
  };
}

interface PushSubscriptionResponse {
  data: string;
}

export function usePushNotification() {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [vapidKey, setVapidKey] = useState<string | null>(null);

  console.log('currentSub: ', subscription);

  // Check if browser supports push notifications
  useEffect(() => {
    const isSupported =
      'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
    setIsSupported(isSupported);

    if (isSupported) {
      initializeServiceWorker();
    }

    if (permission === 'granted' && subscription) {
      subscribeUserToPush();
    }
  }, []);

  // Initialize service worker and check existing subscription
  const initializeServiceWorker = async () => {
    try {
      await registerServiceWorker();
      await checkExistingSubscription();
    } catch (error) {
      console.error('Error initializing push notifications:', error);
    }
  };

  // Register service worker
  const registerServiceWorker = async () => {
    try {
      if (!navigator.serviceWorker.controller) {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/',
          updateViaCache: 'none',
        });
        console.log('Service Worker registered with scope:', registration.scope);
      } else {
        await navigator.serviceWorker.ready;
        console.log('Service Worker already registered');
      }
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      throw error;
    }
  };

  // Check if there's an existing subscription
  const checkExistingSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const existingSubscription = await registration.pushManager.getSubscription();

      if (existingSubscription) {
        setSubscription(JSON.parse(JSON.stringify(existingSubscription)));
        setPermission('granted');
      }
    } catch (error) {
      console.error('Error checking existing subscription:', error);
    }
  };

  const checkSubscriptionOnServer = async () => {
    try {
      const response = await fetch(`${ENDPOINTS.PURRO}/notifications/settings/me`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: subscription,
        }),
      });

      const { data }: PushSubscriptionResponse = await response.json();
      console.log(data);
    } catch (error) {
      console.error('Error checking subscription on server:', error);
    }
  };

  // Get VAPID key from server
  const getVapidKey = async (): Promise<string | null> => {
    try {
      if (vapidKey) return vapidKey;

      const response = await fetch(`${ENDPOINTS.PURRO}/notifications/webpush/vapid-key`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      const { data }: PushSubscriptionResponse = await response.json();
      setVapidKey(data);
      return data;
    } catch (error) {
      console.error('Error fetching VAPID key:', error);
      return null;
    }
  };

  // Request notification permission
  const requestPermission = async () => {
    if (!isSupported) return false;

    try {
      setLoading(true);
      const permissionResult = await Notification.requestPermission();
      setPermission(permissionResult);

      return true;
    } catch (error) {
      console.error('Error requesting permission:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Subscribe user to push notifications
  const subscribeUserToPush = async (): Promise<PushSubscription | null> => {
    if (!isSupported) return null;

    try {
      setLoading(true);

      if (permission !== 'granted') await requestPermission();

      // Get VAPID key
      const publicKey = await getVapidKey();
      if (!publicKey) throw new Error('Failed to get VAPID key');

      // Subscribe to push
      const registration = await navigator.serviceWorker.ready;

      console.log(registration);

      const pushSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(publicKey),
      });

      console.log(pushSubscription);

      // Send subscription to server
      await fetch(`${ENDPOINTS.PURRO}/notifications/webpush/subscribe`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: pushSubscription,
        }),
      });

      const serializedSub = JSON.parse(JSON.stringify(pushSubscription));
      setSubscription(serializedSub);

      alert('Subscribed to push notifications');
      return serializedSub;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    permission,
    subscription,
    loading,
    isSupported,
    requestPermission,
    subscribeUserToPush,
    checkSubscriptionOnServer,
  };
}

// Helper function to convert base64 to Uint8Array
function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

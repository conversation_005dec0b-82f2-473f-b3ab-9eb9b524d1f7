import { useLocation, useNavigate } from 'react-router';
import { Outlet } from 'react-router';
import useNavigatingMemory from '@/store/navigatingMemory';
import { useEffect, useState, useRef } from 'react';
import HyperliquidLogo from '@/assets/logos/hyperliquid_logo.svg';

export default function MarketLayout() {
  const navigate = useNavigate();
  const location = useLocation();

  // State cho touch swipe
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [swipeProgress, setSwipeProgress] = useState(0);

  // Để theo dõi khi nào đang chuyển tab
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Swipe history
  const [hasSwipedBefore, setHasSwipedBefore] = useState(() => {
    return localStorage.getItem('marketSwipedBefore') === 'true';
  });

  useEffect(() => {
    if (swipeProgress !== 0 && !hasSwipedBefore) {
      setHasSwipedBefore(true);
      localStorage.setItem('marketSwipedBefore', 'true');
    }
  }, [swipeProgress, hasSwipedBefore]);

  // Determine active tab based on the current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/hyperevm')) return 'hyperevm';
    return 'spot'; // Default tab
  };

  const activeTab = getActiveTab();
  const tabsRef = useRef(['spot', 'hyperevm']);

  const { setMarketPageMemory } = useNavigatingMemory();

  const handleTabClick = (tab: 'spot' | 'hyperevm') => {
    // Đánh dấu đang trong quá trình chuyển tab
    setIsTransitioning(true);

    // Ghi nhớ tab
    setMarketPageMemory(tab);

    // Điều hướng đến tab mới
    navigate("/market/" + tab);

    // Sau khi chuyển tab, reset trạng thái
    setTimeout(() => {
      setIsTransitioning(false);
      setSwipeProgress(0);
    }, 300); // Thời gian ngắn để chờ quá trình chuyển route hoàn tất
  };

  // Touch handling for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    // Không xử lý khi đang trong quá trình chuyển tab
    if (isTransitioning) return;

    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    // Không xử lý khi đang trong quá trình chuyển tab
    if (isTransitioning || !touchStart) return;

    const currentTouch = e.targetTouches[0].clientX;
    setTouchEnd(currentTouch);

    const delta = touchStart - currentTouch;
    const maxSwipe = 100;
    const progress = Math.max(-1, Math.min(1, delta / maxSwipe));
    setSwipeProgress(progress);
  };

  const handleTouchEnd = () => {
    // Không xử lý khi đang trong quá trình chuyển tab
    if (isTransitioning || !touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    const tabs = tabsRef.current;
    const currentIndex = tabs.indexOf(activeTab);

    // Reset touch values
    setTouchStart(0);
    setTouchEnd(0);

    // Xác định tab tiếp theo dựa trên hướng vuốt
    if (isLeftSwipe && currentIndex < tabs.length - 1) {
      // Vuốt trái (từ trái sang phải) -> tab tiếp theo (phải)
      handleTabClick(tabs[currentIndex + 1] as 'spot' | 'hyperevm');
    } else if (isRightSwipe && currentIndex > 0) {
      // Vuốt phải (từ phải sang trái) -> tab trước đó (trái)
      handleTabClick(tabs[currentIndex - 1] as 'spot' | 'hyperevm');
    } else {
      // Không đủ điều kiện để chuyển tab, reset swipe progress
      setSwipeProgress(0);
    }
  };

  return (
    <div
      className="flex flex-col h-full"

    >
      <div className="bg-background sticky top-0 z-10">
        <div className="border-border flex border-b">
          <button
            className={`flex items-center justify-center gap-1 px-4 py-2 text-base font-medium ${activeTab === 'spot'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => handleTabClick('spot')}
          >
            <img src={HyperliquidLogo} alt="Hyperliquid Logo" className="size-4" />
            Spot
          </button>
          <button
            className={`flex items-center justify-center gap-1 px-4 py-2 text-base font-medium ${activeTab === 'hyperevm'
              ? 'text-primary border-primary border-b-2'
              : 'text-muted-foreground hover:text-foreground'
              }`}
            onClick={() => handleTabClick('hyperevm')}
          >
            <img src={HyperliquidLogo} alt="Hyperliquid Logo" className="size-4" />
            EVM
          </button>
        </div>
      </div>
      {/* Main content - không có transform */}
      <div
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        className="h-full pb-safe">
        <Outlet />
      </div>
    </div>
  );
}
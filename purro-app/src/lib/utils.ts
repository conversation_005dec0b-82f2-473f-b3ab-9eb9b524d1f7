import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Extend Navigator interface to include Safari iOS standalone property
declare global {
  interface Navigator {
    standalone?: boolean;
  }
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function truncateAddress(address: string, length = 6) {
  return `${address.slice(0, length)}...${address.slice(-length)}`;
}

export function isPwa() {
  // Kiểm tra display-mode
  const isStandalone =
    window.matchMedia('(display-mode: standalone)').matches ||
    window.matchMedia('(display-mode: fullscreen)').matches ||
    window.matchMedia('(display-mode: minimal-ui)').matches;

  // Kiểm tra đặc biệt cho iOS
  const isIOSStandalone = window.navigator.standalone === true;

  // Kiểm tra đặc biệt cho Android thông qua manifest
  const hasManifest = document.querySelector('link[rel="manifest"]') !== null;

  // <PERSON><PERSON><PERSON> tra service worker
  const hasServiceWorker = 'serviceWorker' in navigator;

  return isStandalone || isIOSStandalone || (hasManifest && hasServiceWorker);
}

export const getTimeKey = (timeFrame: '5m' | '1h' | '6h' | '24h') => {
  switch (timeFrame) {
    case '5m':
      return 'm5';
    case '1h':
      return 'h1';
    case '6h':
      return 'h6';
    case '24h':
      return 'h24';
    default:
      return 'h24';
  }
};

export function formatNumber(
  num: string | number | null,
  minimumFractionDigits = 2,
  maximumFractionDigits = 2
): string {
  if (num === null || num === undefined) return '-';

  const parsedNum = typeof num === 'string' ? parseFloat(num) : num;

  if (isNaN(parsedNum)) return '-';

  if (parsedNum >= 1e9) {
    return `$${(parsedNum / 1e9).toLocaleString(undefined, {
      minimumFractionDigits,
      maximumFractionDigits,
    })}B`;
  } else if (parsedNum >= 1e6) {
    return `$${(parsedNum / 1e6).toLocaleString(undefined, {
      minimumFractionDigits,
      maximumFractionDigits,
    })}M`;
  } else if (parsedNum >= 1e3) {
    return `$${(parsedNum / 1e3).toLocaleString(undefined, {
      minimumFractionDigits,
      maximumFractionDigits,
    })}K`;
  } else if (parsedNum < 0.01 && parsedNum > 0) {
    return `$${parsedNum.toLocaleString(undefined, {
      minimumFractionDigits: 6,
      maximumFractionDigits: 6,
    })}`;
  } else {
    return `$${parsedNum.toLocaleString(undefined, {
      minimumFractionDigits,
      maximumFractionDigits,
    })}`;
  }
}

export function formatPercentage(value: string | null): string {
  if (!value) return '0.00%';
  return `${parseFloat(value).toFixed(2)}%`;
}

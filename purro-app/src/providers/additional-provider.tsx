import React from 'react';
import LoadingScreen from '@/components/screens/loading-screen';
import { InstallPrompt } from '@/components/common/install-prompt';
import { PullToRefresh } from '@/components/common/pull-to-refresh';
import useHyperliquidConnection from '@/hooks/useHyperliquidSocket';
import { Toaster } from '@/components/ui/sonner';
import ResponsiveDialog from '@/components/responsive-dialog';
import { Captcha, usePrivy } from '@privy-io/react-auth';

const AdditionalProvider = ({ children }: { children: React.ReactNode }) => {
  const { ready } = usePrivy();
  useHyperliquidConnection('mainnet', {
    autoConnect: true
  });

  if (!ready) {
    return <LoadingScreen />;
  }

  return (
    <>
      <PullToRefresh />
      <InstallPrompt />
      {children}
      <Toaster position="top-center" />
      <ResponsiveDialog />
      <Captcha />
    </>
  );
};

export default AdditionalProvider;

import { PrivyProvider } from '@privy-io/react-auth';
import { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { mainnet, sepolia } from 'viem/chains';
import { http } from 'wagmi';
import { WagmiProvider, createConfig } from '@privy-io/wagmi';
import { useTheme } from './theme-provider';

const queryClient = new QueryClient();

const wagmiConfig = createConfig({
  chains: [mainnet, sepolia],
  transports: {
    [mainnet.id]: http(),
    [sepolia.id]: http(),
  },
});

const PrivyGlobalProvider = ({ children }: { children: ReactNode }) => {
  const appId = import.meta.env.VITE_PUBLIC_PRIVY_APP_ID;
  const clientId = import.meta.env.VITE_PUBLIC_PRIVY_CLIENT_ID;
  if (!appId || !clientId) {
    throw new Error('VITE_PUBLIC_PRIVY_APP_ID or VITE_PUBLIC_PRIVY_CLIENT_ID is not defined');
  }
  const { theme } = useTheme();
  let themeName = theme;
  if (theme === 'system') {
    themeName = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  return (
    <PrivyProvider
      appId={appId}
      clientId={clientId}
      config={{
        loginMethods: ['email', 'google', 'twitter', 'discord'],
        embeddedWallets: {
          ethereum: {
            createOnLogin: 'users-without-wallets',
          },
        },
        appearance: {
          theme: themeName as 'dark' | 'light',
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <WagmiProvider config={wagmiConfig}>{children}</WagmiProvider>
      </QueryClientProvider>
    </PrivyProvider>
  );
};

export default PrivyGlobalProvider;

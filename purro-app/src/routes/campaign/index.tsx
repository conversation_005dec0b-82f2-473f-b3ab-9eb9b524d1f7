const CampaignScreen = () => {
    return (
        <div className="w-full h-full p-4">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center text-lg bg-primary">
                        <img src="/apple-touch-icon.png" alt="Logo" className="w-full h-full" />
                    </div>
                    <div>
                        <h2 className="font-semibold text-gray-900 dark:text-foreground text-lg">Purro here!</h2>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                            We live on{' '}
                            <a
                                href="https://app.hyperliquid.xyz/trade/0xaea9bf1dcd2a5de3cd70260ada52c4fa"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="font-medium underline text-primary"
                            >
                                Hyperliquid
                            </a>
                        </p>
                    </div>
                </div>
                <span className="text-xs font-medium px-3 py-1 rounded-full text-primary-foreground bg-primary">
                    APP SOON
                </span>
            </div>

            {/* Phase Timeline */}
            <div className="mb-6">
                <h3 className="font-medium text-gray-900 dark:text-foreground mb-4">Airdrop Phases</h3>

                <div className="space-y-4">
                    {/* Phase 1 - Active */}
                    <div className="flex gap-4">
                        <div className="flex flex-col items-center">
                            <div className="w-8 h-8 rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium bg-primary">
                                1
                            </div>
                            <div className="w-px h-8 bg-gray-300 dark:bg-gray-700 mt-2"></div>
                        </div>
                        <div className="flex-1 pb-4">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-gray-900 dark:text-foreground">Community Building</h4>
                                <span className="text-xs px-2 py-1 rounded-full text-primary-foreground bg-primary animate-pulse">
                                    ACTIVE
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Follow X, join Telegram, Discord</p>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                                Reward: Base allocation
                            </div>
                        </div>
                    </div>

                    {/* Phase 2 */}
                    <div className="flex gap-4">
                        <div className="flex flex-col items-center">
                            <div className="w-8 h-8 rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium bg-primary">
                                2
                            </div>
                            <div className="w-px h-8 bg-gray-300 dark:bg-gray-700 mt-2"></div>
                        </div>
                        <div className="flex-1 pb-4">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-gray-900 dark:text-foreground">Token Holding</h4>
                                <span className="text-xs px-2 py-1 rounded-full text-primary-foreground bg-primary animate-pulse">
                                    ACTIVE
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Hold 1,000+ PURRO tokens ($160+) for 30+ days</p>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                                Reward: 2x multiplier + holder benefits
                            </div>
                        </div>
                    </div>

                    {/* Phase 3 */}
                    <div className="flex gap-4">
                        <div className="flex flex-col items-center">
                            <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                3
                            </div>
                            <div className="w-px h-8 bg-gray-200 dark:bg-gray-700 mt-2"></div>
                        </div>
                        <div className="flex-1 pb-4">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-gray-700 dark:text-gray-300">On-chain Activity</h4>
                                <span className="text-xs px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                    SOON
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Active trading and staking on Purro</p>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                                Reward: 3x multiplier + trading benefits
                            </div>
                        </div>
                    </div>

                    {/* Phase 4 */}
                    <div className="flex gap-4">
                        <div className="flex flex-col items-center">
                            <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                4
                            </div>
                        </div>
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-gray-700 dark:text-gray-300">Elite Network</h4>
                                <span className="text-xs px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                    SOON
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Successful referrals, community contributions</p>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                                Reward: 5x multiplier + exclusive access
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Actions */}
            {/* <div className="flex flex-col sm:flex-row gap-3 mb-6">
                <button
                    className="px-6 py-2 rounded-lg text-primary-foreground font-medium hover:opacity-90 transition-opacity flex-1 bg-primary"
                >
                    Start Phase 1 & 2
                </button>
                <button className="px-6 py-2 rounded-lg border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    Check My Progress
                </button>
            </div> */}

            {/* Leaderboard */}
            {/* <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-gray-900 dark:text-foreground">🏆 Top Contributors</h3>
                    <button className="text-sm underline hover:no-underline text-primary">
                        View all
                    </button>
                </div>

                <div className="space-y-2">
                    <div className="flex items-center justify-between py-2">
                        <div className="flex items-center gap-2">
                            <span>🥇</span>
                            <span className="text-gray-700 dark:text-gray-300 text-sm">vitalik</span>
                        </div>
                        <span className="text-sm font-medium text-primary">2,450 pts</span>
                    </div>

                    <div className="flex items-center justify-between py-2">
                        <div className="flex items-center gap-2">
                            <span>🥈</span>
                            <span className="text-gray-700 dark:text-gray-300 text-sm">degen</span>
                        </div>
                        <span className="text-sm font-medium text-primary">1,890 pts</span>
                    </div>

                    <div className="flex items-center justify-between py-2">
                        <div className="flex items-center gap-2">
                            <span>🥉</span>
                            <span className="text-gray-700 dark:text-gray-300 text-sm">whale</span>
                        </div>
                        <span className="text-sm font-medium text-primary">1,650 pts</span>
                    </div>
                </div>
            </div> */}
        </div>
    )
}

export default CampaignScreen
import exploreLinks from "./exploreLinks";
export default function ExploreScreen() {
  return (
    <>
      <div>

      </div>
      <div className="pb-safe mb-20 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 px-2">
        {exploreLinks.map((link) => (
          <a key={link.name} href={link.href} target="_blank" className="bg-card rounded-lg p-2 md:p-4 hover:bg-primary/10 hover:text-primary transition-colors duration-200 ease-linear space-y-2">
            <div className="flex items-center  gap-2">
              <img src={link.logo} alt={link.name} className="size-6 md:size-8" />
              <p className="text-base md:text-lg font-semibold">{link.name}</p>         </div>
            <p className="text-xs md:text-sm text-muted-foreground line-clamp-1 md:line-clamp-2">{link.description}</p>
          </a>
        ))}
      </div>
    </>

  );
}

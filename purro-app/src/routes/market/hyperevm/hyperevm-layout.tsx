import { Flame } from "lucide-react";
import { Outlet, useLocation, useNavigate } from "react-router";
import useMarketHyperEvmTabStore, { MarketHyperEvmTab } from "@/store/marketHyperEvmTab";
import { useEffect, useRef, useState } from "react";
import HyperSwapLogo from "@/assets/logos/hyperswap.svg";
import LaminarLogo from "@/assets/logos/laminar.png";
import KittenswapLogo from "@/assets/logos/kittenswap.jpg";

const HyperEvmTab = () => {
    const location = useLocation();
    const { currentTab, setCurrentTab } = useMarketHyperEvmTabStore();
    const navigate = useNavigate();

    // Lưu trạng thái trước đó để so sánh
    const previousTabRef = useRef(currentTab);
    const previousSearchRef = useRef(location.search);

    const tabsRef = useRef(['trending', 'hyperswap-v3', 'hyperswap-v2', 'laminar', 'kittenswap']);
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);
    const [swipeProgress, setSwipeProgress] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);

    // Refs cho các tab buttons để scroll
    const tabsContainerRef = useRef<HTMLDivElement>(null);
    const tabButtonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

    // Swipe history
    const [hasSwipedBefore, setHasSwipedBefore] = useState(() => {
        return localStorage.getItem('marketHasSwipedBefore') === 'true';
    });

    useEffect(() => {
        if (swipeProgress !== 0 && !hasSwipedBefore) {
            setHasSwipedBefore(true);
            localStorage.setItem('marketHasSwipedBefore', 'true');
        }
    }, [swipeProgress, hasSwipedBefore]);

    // Đồng bộ tối ưu giữa URL và State
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const tabFromUrl = searchParams.get('t') as MarketHyperEvmTab;

        // Kiểm tra xem URL có thay đổi không
        if (location.search !== previousSearchRef.current) {
            // URL đã thay đổi, cập nhật state nếu cần
            previousSearchRef.current = location.search;

            if (tabFromUrl && tabsRef.current.includes(tabFromUrl) && tabFromUrl !== currentTab) {
                setCurrentTab(tabFromUrl);
                previousTabRef.current = tabFromUrl;
            }
        }
        // Kiểm tra xem state có thay đổi không và URL hiện tại không khớp với state
        else if (currentTab !== previousTabRef.current) {
            // State đã thay đổi, cập nhật URL
            previousTabRef.current = currentTab;

            const currentUrlTab = searchParams.get('t');
            if (currentTab && currentUrlTab !== currentTab) {
                navigate({ search: `?t=${currentTab}` }, { replace: true });
            }
        }
        // Trường hợp đặc biệt: khởi tạo khi không có search param nhưng có state
        else if (!searchParams.has('t') && currentTab) {
            navigate({ search: `?t=${currentTab}` }, { replace: true });
        }
    }, [location.search, currentTab, navigate, setCurrentTab]);

    // Scroll to active tab whenever currentTab changes
    useEffect(() => {
        if (currentTab && tabButtonRefs.current[currentTab] && tabsContainerRef.current) {
            const container = tabsContainerRef.current;
            const button = tabButtonRefs.current[currentTab];

            // Tính toán vị trí scroll
            const containerWidth = container.offsetWidth;
            const buttonLeft = button.offsetLeft;
            const buttonWidth = button.offsetWidth;

            // Căn giữa button trong container nếu có thể
            // Nếu không thì đảm bảo button hiển thị
            const scrollPosition = Math.max(
                0,
                Math.min(
                    buttonLeft - (containerWidth / 2 - buttonWidth / 2),
                    container.scrollWidth - containerWidth
                )
            );

            // Scroll mượt đến vị trí đó
            container.scrollTo({
                left: scrollPosition,
                behavior: 'smooth'
            });
        }
    }, [currentTab]);

    // Touch handling
    const handleTouchStart = (e: React.TouchEvent) => {
        if (isTransitioning) return;
        setTouchStart(e.targetTouches[0].clientX);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        if (isTransitioning || !touchStart) return;
        const currentTouch = e.targetTouches[0].clientX;
        setTouchEnd(currentTouch);

        if (touchStart) {
            const delta = touchStart - currentTouch;
            const maxSwipe = 100;
            const progress = Math.max(-1, Math.min(1, delta / maxSwipe));
            setSwipeProgress(progress);
        }
    };

    const handleTouchEnd = () => {
        if (isTransitioning || !touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > 50;
        const isRightSwipe = distance < -50;

        const tabs = tabsRef.current;
        const currentIndex = tabs.indexOf(currentTab);

        // Reset touch values
        setTouchStart(0);
        setTouchEnd(0);

        // Xử lý vuốt trái khi ở tab đầu tiên - điều hướng đến route khác
        if (isRightSwipe && currentIndex === 0) {
            // Đánh dấu đang chuyển tab để ngăn xử lý sự kiện vuốt trùng lặp
            setIsTransitioning(true);

            // Vuốt sang phải ở tab đầu tiên, điều hướng đến /market/spot
            setTimeout(() => {
                navigate('/market/spot');
                setSwipeProgress(0);
            }, 50);
            return;
        }

        if (isLeftSwipe && currentIndex < tabs.length - 1) {
            setIsTransitioning(true);
            handleTabClick(tabs[currentIndex + 1] as MarketHyperEvmTab);
            setTimeout(() => {
                setIsTransitioning(false);
                setSwipeProgress(0);
            }, 300);
        } else if (isRightSwipe && currentIndex > 0) {
            setIsTransitioning(true);
            handleTabClick(tabs[currentIndex - 1] as MarketHyperEvmTab);
            setTimeout(() => {
                setIsTransitioning(false);
                setSwipeProgress(0);
            }, 300);
        } else {
            setSwipeProgress(0);
        }
    };

    const handleTabClick = (tab: MarketHyperEvmTab) => {
        if (tab !== currentTab) {
            setCurrentTab(tab);
        }
    };

    return (
        <>
            <div className="bg-background mb-2">
                <div
                    ref={tabsContainerRef}
                    className="border-border flex border-b whitespace-nowrap overflow-x-auto no-scrollbar"
                >
                    <button
                        ref={(el) => { tabButtonRefs.current['trending'] = el; }}
                        className={`flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium ${currentTab === 'trending'
                            ? 'text-primary border-primary border-b-2'
                            : 'text-muted-foreground hover:text-foreground'
                            }`}
                        onClick={() => handleTabClick('trending')}
                    >
                        <Flame className="size-4 text-red-500" />
                        Trending
                    </button>
                    <button
                        ref={(el) => { tabButtonRefs.current['hyperswap-v3'] = el; }}
                        className={`flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium ${currentTab === 'hyperswap-v3'
                            ? 'text-primary border-primary border-b-2'
                            : 'text-muted-foreground hover:text-foreground'
                            }`}
                        onClick={() => handleTabClick('hyperswap-v3')}
                    >
                        <img src={HyperSwapLogo} alt="Hyperliquid Logo" className="size-4" />
                        HyperSwap V3
                    </button>

                    <button
                        ref={(el) => { tabButtonRefs.current['hyperswap-v2'] = el; }}
                        className={`flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium ${currentTab === 'hyperswap-v2'
                            ? 'text-primary border-primary border-b-2'
                            : 'text-muted-foreground hover:text-foreground'
                            }`}
                        onClick={() => handleTabClick('hyperswap-v2')}
                    >
                        <img src={HyperSwapLogo} alt="Hyperliquid Logo" className="size-4" />
                        HyperSwap V2
                    </button>

                    <button
                        ref={(el) => { tabButtonRefs.current['laminar'] = el; }}
                        className={`flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium ${currentTab === 'laminar'
                            ? 'text-primary border-primary border-b-2'
                            : 'text-muted-foreground hover:text-foreground'
                            }`}
                        onClick={() => handleTabClick('laminar')}
                    >
                        <img src={LaminarLogo} alt="Hyperliquid Logo" className="size-4" />
                        Laminar
                    </button>

                    <button
                        ref={(el) => { tabButtonRefs.current['kittenswap'] = el; }}
                        className={`flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium ${currentTab === 'kittenswap'
                            ? 'text-primary border-primary border-b-2'
                            : 'text-muted-foreground hover:text-foreground'
                            }`}
                        onClick={() => handleTabClick('kittenswap')}
                    >
                        <img src={KittenswapLogo} alt="Hyperliquid Logo" className="size-4" />
                        Kittenswap
                    </button>
                </div>
            </div>

            <div onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}>
                <Outlet />
            </div>
        </>
    );
};

export default HyperEvmTab;
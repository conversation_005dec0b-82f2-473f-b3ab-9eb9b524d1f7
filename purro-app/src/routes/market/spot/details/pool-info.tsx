import { cn } from '@/lib/utils';
import { formatBigAmount } from '@/utils/formatters';
import React, { useState } from 'react';

interface PoolInfoProps {
  poolData: {
    name: string;
    token_price_usd: string;
    market_cap_usd: string;
    fdv_usd: string;
    pool_created_at: string;
    price_change_percentage: {
      m5: string;
      m15: string;
      m30: string;
      h1: string;
      h6: string;
      h24: string;
    };
    volume_usd: {
      m5: string;
      m15: string;
      m30: string;
      h1: string;
      h6: string;
      h24: string;
    };
    transactions: {
      m5: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
      m15: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
      m30: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
      h1: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
      h6: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
      h24: {
        buys: number;
        sells: number;
        buyers: number;
        sellers: number;
      };
    };
  };
}

interface TimeFrameButtonProps {
  timeFrame: string;
  currentTimeFrame: string;
  priceChange: number;
  onClick: () => void;
  label: string;
}

const TimeFrameButton: React.FC<TimeFrameButtonProps> = ({
  timeFrame,
  currentTimeFrame,
  priceChange,
  onClick,
  label
}) => {
  const isActive = currentTimeFrame === timeFrame;
  const isPositive = priceChange > 0;
  const isNegative = priceChange < 0;

  return (
    <button
      onClick={onClick}
      className={cn(
        'px-2 py-4 text-sm font-medium w-full transition-colors',
        'hover:bg-muted/50',
        {
          'bg-primary/10 text-primary': isActive && priceChange === 0,
          'bg-green-500/10 text-green-500': isActive && isPositive,
          'bg-red-500/10 text-red-500': isActive && isNegative,
          'hover:bg-green-500/5': !isActive && isPositive,
          'hover:bg-red-500/5': !isActive && isNegative,
        },
      )}
    >
      <div className="flex flex-col items-center">
        <span>{label}</span>
        <p
          className={cn('text-xs mt-1', {
            'text-green-500': isPositive,
            'text-red-500': isNegative,
            'text-muted-foreground': priceChange === 0,
          })}
        >
          {priceChange > 0 ? '+' : ''}{priceChange.toFixed(2)}%
        </p>
      </div>
    </button>
  );
};

interface PoolStatsProps {
  poolData: PoolInfoProps['poolData'];
  timeFrame: string;
}

const PoolStats: React.FC<PoolStatsProps> = ({ poolData, timeFrame }) => {
  console.log(poolData);
  const getTimeFrameKey = (tf: string) => {
    switch (tf) {
      case '30m': return 'm30';
      case '1h': return 'h1';
      case '6h': return 'h6';
      case '24h': return 'h24';
      default: return 'm30';
    }
  };

  const key = getTimeFrameKey(timeFrame);
  const volume = poolData.volume_usd[key as keyof typeof poolData.volume_usd];
  const transactions = poolData.transactions[key as keyof typeof poolData.transactions];

  return (
    <div className="grid grid-cols-2 border-b">
      <div className='p-2 border-b border-r'>
        <h3 className="text-xs">Vol</h3>
        <p className="text-sm font-semibold">{formatBigAmount(volume)}</p>
      </div>
      <div className='p-2 border-b'>
        <h3 className="text-xs">Maker</h3>
        <p className="text-sm">{(transactions.buyers + transactions.sellers).toLocaleString()}</p>
      </div>
      <div className='p-2 border-r'>
        <h3 className="text-xs">Txns</h3>
        <p className="text-sm">{(transactions.buys + transactions.sells).toLocaleString()}</p>
      </div>
      <div className='p-2'>
        <div className="text-sm">
          <p ><span className="text-green-500">BUY</span> {transactions.buys.toLocaleString()}</p>
          <p><span className="text-red-500">SELL</span> {transactions.sells.toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

const PoolInfo: React.FC<PoolInfoProps> = ({ poolData }) => {
  const [timeFrame, setTimeFrame] = useState<'30m' | '1h' | '6h' | '24h'>('30m');

  const timeFrames = [
    { key: '30m', label: '30m', change: parseFloat(poolData.price_change_percentage.m30) },
    { key: '1h', label: '1h', change: parseFloat(poolData.price_change_percentage.h1) },
    { key: '6h', label: '6h', change: parseFloat(poolData.price_change_percentage.h6) },
    { key: '24h', label: '24h', change: parseFloat(poolData.price_change_percentage.h24) },
  ];

  return (
    <div className="w-full border-b">
      <div className="flex items-center w-full border-b">
        {timeFrames.map((tf) => (
          <TimeFrameButton
            key={tf.key}
            timeFrame={tf.key}
            currentTimeFrame={timeFrame}
            priceChange={tf.change}
            onClick={() => setTimeFrame(tf.key as '30m' | '1h' | '6h' | '24h')}
            label={tf.label}
          />
        ))}
      </div>

      <PoolStats poolData={poolData} timeFrame={timeFrame} />

      <div className='grid grid-cols-2 mt-2 border-t'>
        <div className='p-2 border-b border-r'>
          <h3 className="text-xs">MCAP</h3>
          <p className="text-sm font-semibold">{formatBigAmount(poolData.market_cap_usd)}</p>
        </div>
        <div className='p-2 border-b'>
          <h3 className="text-xs">FDV</h3>
          <p className="text-sm font-semibold">{formatBigAmount(poolData.fdv_usd)}</p>
        </div>
      </div>
    </div>
  );
};

export default PoolInfo;
import { BellIcon, Book, ChevronRightIcon, ExternalLinkIcon, FileKey, Lock, Paintbrush, RotateCwIcon, Scroll } from "lucide-react"
import packageJson from '../../../package.json';
import { Link, Navigate, useNavigate } from "react-router";
import HeaderBackBtn from "@/components/navigation/header-back-btn";
import { useMediaQuery } from "react-responsive";
import TelegramIcon from "@/assets/icons/TelegramIcon";
import XIcon from "@/assets/icons/XIcon";
import DiscordIcon from "@/assets/icons/DiscordIcon";
import { usePrivy } from "@privy-io/react-auth";
import { useMfaEnrollment } from "@privy-io/react-auth";
import { LogOutIcon } from "lucide-react";

const MobileSettingsScreen = () => {
    const { showMfaEnrollmentModal } = useMfaEnrollment();
    const { authenticated, logout } = usePrivy();
    const navigate = useNavigate();
    const isMobile = useMediaQuery({ query: '(max-width: 768px)' })

    if (!isMobile) {
        return <Navigate to="/settings" />
    }

    return (
        <main >
            <HeaderBackBtn title="Settings" />

            <div className="space-y-2 pt-3 px-2">
                <div className="bg-primary rounded-2xl" onClick={() => {
                    window.location.reload();
                }}>
                    <div className="flex items-center gap-3 p-4">
                        <RotateCwIcon className="size-5 text-white" />
                        <p className="text-base text-white">Refresh the App</p>
                    </div>
                </div>
                {authenticated && (
                    <div className="bg-card rounded-2xl">
                        <div className="flex items-center justify-between gap-3 pl-4" onClick={() => showMfaEnrollmentModal()}>
                            <Lock className="size-5" />
                            <div className={`flex items-center gap-3 w-full py-4 pr-4`}>
                                <p className="text-base w-full">MFA</p>
                            </div>
                        </div>
                    </div>
                )}
                <div className="bg-card rounded-2xl">
                    <MobileListButton icon={<BellIcon className="size-5" />} label="Notifications" href="/mobile-settings/notifications" isBottomBorder />
                    <MobileListButton icon={<Paintbrush className="size-5" />} label="Appearance" href="/mobile-settings/appearance" />
                </div>
                <div className="bg-card rounded-2xl">
                    <MobileListButton icon={<Book className="size-5" />} label="Documentation" href="https://docs.purro.xyz" isBottomBorder iconRight={<ExternalLinkIcon className="size-5" />} />
                    <MobileListButton icon={<Scroll className="size-5" />} label="Terms of Service" href="/terms" isBottomBorder />
                    <MobileListButton icon={<FileKey className="size-5" />} label="Privacy Policy" href="/privacy" />
                </div>

                <div className="bg-card rounded-2xl">
                    <MobileListButton icon={<XIcon className="size-5" />} label="X" href="https://x.com/purro_xyz" isBottomBorder iconRight={<ExternalLinkIcon className="size-5" />} />
                    <MobileListButton icon={<TelegramIcon className="size-5" />} label="Telegram" href="https://t.me/purro_xyz" isBottomBorder iconRight={<ExternalLinkIcon className="size-5" />} />
                    <MobileListButton icon={<DiscordIcon className="size-5" />} label="Discord" href="https://discord.gg/purro" iconRight={<ExternalLinkIcon className="size-5" />} />
                </div>


                <div className="bg-destructive rounded-2xl" onClick={() => {
                    logout();
                    navigate('/');
                }}>
                    <div className="flex items-center gap-3 p-4">
                        <LogOutIcon className="size-5 text-white" />
                        <p className="text-base text-white">Log out</p>
                    </div>
                </div>

            </div>
            <div className="text-muted-foreground mt-4 text-center text-xs">v{packageJson.version}</div>
        </main>
    )
}

function MobileListButton({ icon, label, href, isBottomBorder, iconRight }: { icon: React.ReactNode, label: string, href: string, isBottomBorder?: boolean, iconRight?: React.ReactNode }) {
    return (
        <Link to={href} className="flex items-center justify-between gap-3 pl-4">
            {icon}
            <div className={`flex items-center gap-3 w-full py-4 pr-4 ${isBottomBorder ? 'border-b border-border' : ''}`}>
                <p className="text-base w-full">{label}</p>
                {iconRight ?? <ChevronRightIcon className="size-5" />}
            </div>
        </Link>
    )
}

export default MobileSettingsScreen
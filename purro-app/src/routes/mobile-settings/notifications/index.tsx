import NotificationSettings from "@/components/settings/notifications"
import HeaderBackBtn from "@/components/navigation/header-back-btn"
import { usePushNotification } from "@/hooks/usePushNotification"
import { useEffect } from "react";

const Notifications = () => {
    const { checkSubscriptionOnServer } = usePushNotification();
    useEffect(() => {
        checkSubscriptionOnServer();
    }, []);

    return (
        <div>
            <HeaderBackBtn title="Notifications" />
            <div className="w-full px-4 pt-2">
                <NotificationSettings />
            </div>
        </div>
    )
}

export default Notifications
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { ChevronLeftIcon } from 'lucide-react';

const Privacy = () => {
    const navigate = useNavigate();
    const [privacyContent, setPrivacyContent] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(true);

    useEffect(() => {
        const fetchPolicy = async () => {
            try {
                const response = await fetch('/docs/privacy.txt');
                const text = await response.text();
                setPrivacyContent(text);
            } catch (error) {
                console.error('Error fetching policy:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchPolicy();
    }, []);



    return (
        <div className="container mx-auto px-4 py-8 h-screen">
            <div className="fixed top-0 left-0 right-0 z-50 border-b border-border h-12 backdrop-blur">
                <div className="container mx-auto flex items-center gap-2">
                    <button className="py-3 px-2" onClick={() => navigate(-1)}>
                        <ChevronLeftIcon />
                    </button>
                    <h1 className="text-xl font-semibold">Privacy Policy</h1>
                </div>
            </div>
            <pre className="whitespace-pre-wrap font-sans mt-12 pb-12">
                {isLoading ? <div className="p-4">Loading policy...</div> : privacyContent}
            </pre>
        </div>
    );
};

export default Privacy;
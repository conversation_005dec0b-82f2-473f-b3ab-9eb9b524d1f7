import { Bell, Info, Link2, Lock, LogOut, Paintbrush } from 'lucide-react';
import { useState } from 'react';
import Notifications from './notifications';
import Appearance from './appearance';
import About from './about';
import Connect from './connect';
import { useMediaQuery } from 'react-responsive';
import { Navigate, useNavigate } from 'react-router';
import Security from './security';
import { usePrivy } from '@privy-io/react-auth';

type Settings = 'notifications' | 'appearance' | 'about' | 'connect' | 'security';

const SettingsPageLayout = () => {
  const [setting, setSetting] = useState<Settings>('security');
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' })
  const { logout } = usePrivy();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  }

  if (isMobile) {
    return <Navigate to="/mobile-settings" />
  }

  return (
    <div className="flex h-full flex-col md:flex-row p-2">
      <div className="bg-background border-border sticky top-0 z-10 mb-4 flex justify-start border-b w-50 flex-col border-r">
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'security'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('security')}
        >
          <Lock className="size-5" />
          Security
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'notifications'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('notifications')}
        >
          <Bell className="size-5" />
          Notifications
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'appearance'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('appearance')}
        >
          <Paintbrush className="size-5" />
          Appearance
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'connect'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('connect')}
        >
          <Link2 className="size-5" />
          Connect
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium ${setting === 'about'
            ? 'text-primary border-primary border-r-2'
            : 'text-muted-foreground hover:text-foreground'
            }`}
          onClick={() => setSetting('about')}
        >
          <Info className="size-5" />
          About
        </button>
        <button
          className={`flex items-center justify-start gap-2 px-3 py-2 text-base font-medium text-destructive hover:text-muted-foreground`}
          onClick={handleLogout}
        >
          <LogOut className="size-5" />
          Logout
        </button>
      </div>
      <div className="flex-1">
        <div className="xl:max-w-[40%]">
          {setting === 'notifications' && <Notifications />}
          {setting === 'appearance' && <Appearance />}
          {setting === 'connect' && <Connect />}
          {setting === 'about' && <About />}
          {setting === 'security' && <Security />}
        </div>
      </div>
    </div>
  );
};

export default SettingsPageLayout;

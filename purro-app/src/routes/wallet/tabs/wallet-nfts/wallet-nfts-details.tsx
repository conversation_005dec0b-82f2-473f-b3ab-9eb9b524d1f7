
import { StillBuilding } from "@/components/common/still-building";
// import { ChevronLeft } from "lucide-react";
// import { useNavigate } from "react-router";
import { useParams } from "react-router";

const WalletNftsDetails = () => {
    const params = useParams();
    // const navigate = useNavigate();

    console.log(params);
    return (
        // <div className="px-2">
        //     <div className="flex items-center gap-2 mb-4">
        //         <div className="py-1 px-2 hover:bg-card rounded-2xl active:bg-card transition-all" onClick={() => navigate(-1)}><ChevronLeft /> </div>
        //         <h1 className="text-lg font-semibold">Wallet NFTs Details</h1>
        //     </div>
        //     <p className="text-muted-foreground">{params.address}</p>
        // </div>
        <StillBuilding />
    )
}

export default WalletNftsDetails
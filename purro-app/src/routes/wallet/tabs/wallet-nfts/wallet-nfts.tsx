import { useState, useEffect } from "react";
import useHyperEVM from "@/hooks/useHyperEVM";
import { Link } from "react-router";
import { HyperScanNftNextPageParams } from "@/types/hyperEvm";
import Pagination from "@/components/pagination";
import CachedImage from "@/components/cached-image";
import { useAccount } from "wagmi";

const WalletNFTs = () => {
    const { useNFTs } = useHyperEVM();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageParams, setPageParams] = useState<Record<number, HyperScanNftNextPageParams | undefined>>({
        1: undefined // First page has no params
    });
    const { address } = useAccount();

    const { data, isLoading } = useNFTs(address || '',
        pageParams[currentPage]
    );

    // Store next page parameters when data changes
    useEffect(() => {
        if (data?.next_page_params) {
            setPageParams(prev => ({
                ...prev,
                [currentPage + 1]: data.next_page_params
            }));
        }
    }, [data, currentPage]);

    // Handle page navigation
    const goToNextPage = () => {
        if (data?.next_page_params) {
            setCurrentPage(prev => prev + 1);
        }
    };

    const goToPrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(prev => prev - 1);
        }
    };

    if (isLoading) {
        return (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 px-2">
                {[...Array(10)].map((_, index) => (
                    <div key={`skeleton-${index}`} className="aspect-square bg-gray-200 animate-pulse rounded-md"></div>
                ))}
            </div>
        );
    }

    if (!data?.items?.length) {
        return <div className="text-center py-8">No NFTs found</div>;
    }

    return (
        <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 px-2">
                {data.items.map((item, index) => (
                    <Link key={`nft-${item.id}`} className="aspect-square rounded-lg overflow-hidden bg-card hover:scale-105 transition-transform cursor-pointer" to={`/wallet/nfts/${item.token.address}`}>
                        {item.image_url ? (
                            <div className="relative size-full">
                                <CachedImage
                                    src={item.image_url}
                                    alt={item.token.name || `NFT #${index}`}
                                    className="size-full object-contain"
                                />
                                <div className="absolute bottom-0 left-0 right-0  text-white text-left my-1 mx-2 flex items-center justify-between gap-1 w-full">
                                    <p className="rounded-lg text-base bg-primary/90 px-2">
                                        {item.token.name} #{item.id}
                                    </p>
                                    {item.token_type === "ERC-1155" && (
                                        <p className="text-sm font-normal bg-primary/90 px-2 mx-3 rounded-full z-10">{item.value}</p>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="w-full h-full flex items-center justify-center bg-card text-muted-foreground relative px-2 text-center">
                                {item.token.name}
                                <div className="absolute bottom-0 right-0 text-white text-left my-1 mx-2 flex items-center justify-right gap-1">
                                    {item.token_type === "ERC-1155" && (
                                        <p className="text-sm font-normal bg-primary/90 px-2 rounded-full">{item.value}</p>
                                    )}
                                </div>
                            </div>
                        )}
                    </Link>
                ))}
            </div>
            <div className="flex justify-center items-center mt-4 px-2">
                <Pagination
                    currentPage={currentPage}
                    goToNextPage={goToNextPage}
                    goToPrevPage={goToPrevPage}
                    disabled={!data?.next_page_params}
                />
            </div>
        </>
    );
};

export default WalletNFTs;
import { usePortfolioData } from '@/hooks/usePortfolioData';
import TabsLoading from './tabs-loading';
import TabsError from './tabs-error';
import { formatCurrency } from '@/utils/formatters';
import { useAccount } from 'wagmi';

// Define types for position data
interface Position {
  coin: string;
  cumFunding: {
    allTime: string;
    sinceChange: string;
    sinceOpen: string;
  };
  entryPx: string;
  leverage: {
    rawUsd: string;
    type: string;
    value: number;
  };
  liquidationPx: string;
  marginUsed: string;
  maxLeverage: number;
  positionValue: string;
  returnOnEquity: string;
  szi: string;
  unrealizedPnl: string;
}

interface AssetPosition {
  position: Position;
  type: string;
}

const WalletTabsPerps = () => {
  const { address } = useAccount();
  const { perpsData, isPerpsLoading, perpsError } = usePortfolioData(address || '');

  if (isPerpsLoading) {
    return <TabsLoading />;
  }

  if (perpsError) {
    return <TabsError message="Failed to load perps data" />;
  }

  return (
    <div className="space-y-6 px-2">
      {/* <div className='absolute top-2 right-2 z-10'>
                <RefreshTimer onRefresh={refetch} />
            </div> */}

      {/* Account Summary */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-2 md:grid-cols-4">
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Account Value</div>
            <div className="font-semibold text-lg">
              {formatCurrency(parseFloat(perpsData?.marginSummary?.accountValue || '0'))}
            </div>
          </div>
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Margin Used</div>
            <div className="font-semibold text-lg">
              {formatCurrency(parseFloat(perpsData?.marginSummary?.totalMarginUsed || '0'))}
            </div>
          </div>
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Total Position</div>
            <div className="font-semibold text-lg">
              {formatCurrency(parseFloat(perpsData?.marginSummary?.totalNtlPos || '0'))}
            </div>
          </div>
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Withdrawable</div>
            <div className="font-semibold text-lg">
              {formatCurrency(parseFloat(perpsData?.withdrawable || '0'))}
            </div>
          </div>
        </div>
      </div>

      {/* Positions */}
      <>
        {perpsData?.assetPositions && perpsData.assetPositions.length > 0 ? (
          <div className="space-y-2 overflow-visible">
            {perpsData.assetPositions.map((asset: AssetPosition, index: number) => (
              <div key={index} className="bg-card rounded-lg p-3">
                <div className="mb-2 flex justify-between">
                  <div className="min-w-0 flex-1 overflow-hidden">
                    <div className="truncate font-semibold">{asset.position.coin}</div>
                    <div className="text-muted-foreground mt-0.5 truncate text-xs">
                      Leverage: {asset.position.leverage.value}x
                    </div>
                  </div>
                  <div
                    className={`font-semibold ${parseFloat(asset.position.szi) > 0 ? 'text-green-500' : 'text-red-500'}`}
                  >
                    {parseFloat(asset.position.szi) > 0 ? 'Long' : 'Short'}{' '}
                    {formatCurrency(Math.abs(parseFloat(asset.position.szi)))}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Entry Price:</span>{' '}
                    {formatCurrency(parseFloat(asset.position.entryPx))}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Leverage:</span>{' '}
                    {asset.position.leverage.value}x
                  </div>
                  <div>
                    <span className="text-muted-foreground">Liquidation Price:</span>{' '}
                    {formatCurrency(parseFloat(asset.position.liquidationPx))}
                  </div>
                  <div>
                    <span className="text-muted-foreground">PnL:</span>{' '}
                    <span
                      className={
                        parseFloat(asset.position.unrealizedPnl) >= 0
                          ? 'text-green-500'
                          : 'text-red-500'
                      }
                    >
                      {formatCurrency(parseFloat(asset.position.unrealizedPnl))}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-card text-muted-foreground rounded-lg p-3 text-center">
            No open positions
          </div>
        )}
      </>
    </div>
  );
};

export default WalletTabsPerps;

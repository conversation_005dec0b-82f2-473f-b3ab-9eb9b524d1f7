import { usePortfolioData } from '@/hooks/usePortfolioData';
import { formatCurrency } from '@/utils/formatters';
import { ArrowDown, ArrowUp, ArrowRightLeft, GitCompareArrows } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import { useState } from 'react';
import BuildingDialog from '@/components/dialog/building-dialog';
import { useAccount } from 'wagmi';

type WalletActionProps = {
  icon: LucideIcon;
  label: string;
  onClick?: () => void;
};

const walletActions: WalletActionProps[] = [
  {
    icon: ArrowDown,
    label: 'Receive',
  },
  {
    icon: ArrowUp,
    label: 'Send',
  },
  {
    icon: ArrowRightLeft,
    label: 'Swap',
  },
  {
    icon: GitCompareArrows,
    label: 'Bridge',
  },
];

const WalletAction = ({ icon: Icon, label, onClick }: WalletActionProps) => {
  return (
    <div
      className="hover:bg-primary/10 active:bg-primary/10 flex cursor-pointer flex-col items-center gap-1 p-4 transition-all duration-300 hover:opacity-90 active:scale-105"
      onClick={onClick}
    >
      <Icon size={22} className="text-primary/90" />
      <p className="text-primary/90 text-sm font-medium">{label}</p>
    </div>
  );
};

export default function WalletController() {
  const { address } = useAccount();
  const { totalValue } = usePortfolioData(address || '');
  const [isBuilding, setIsBuilding] = useState(false);

  return (
    <div className="relative h-48 md:h-52">
      {/* Phần header có background màu primary */}
      <div className="bg-primary h-36 md:h-42 p-5 md:rounded-br-3xl md:rounded-bl-3xl">
        <div className="flex flex-col items-center justify-between gap-2 mt-2">
          <p className="text-center text-5xl font-semibold text-white md:text-6xl">
            {formatCurrency(totalValue)}
          </p>
          {/* <p className="text-primary w-fit rounded-full bg-white px-2 py-1 text-sm">+3.288%</p> */}
        </div>
      </div>

      {/* WalletActions được đặt ở vị trí absolute, nằm chính giữa đường kẻ dưới */}
      <div className="shadow-accent bg-card absolute left-1/2 w-[90%] -translate-x-1/2 -translate-y-1/2 transform overflow-hidden rounded-3xl md:w-[80%] lg:w-[60%] xl:w-[40%]">
        <div className="grid grid-cols-4">
          {walletActions.map((action, index) => (
            <WalletAction
              key={index}
              icon={action.icon}
              label={action.label}
              onClick={() => setIsBuilding(true)}
            />
          ))}
        </div>
      </div>
      <BuildingDialog isOpen={isBuilding} onOpenChange={setIsBuilding} />
    </div>
  );
}

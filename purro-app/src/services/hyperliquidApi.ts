import { SpotTokenDetails } from '@/types/hyperliquidSpot';
import { ENDPOINTS } from './endpoints';
import { calculateCandleTimeRange } from '@/utils/hyperliquidUtils';

export const fetchSpotAssetsContext = async () => {
  const response = await fetch(`${ENDPOINTS.HYPERLIQUID_L1}/info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      type: 'spotMetaAndAssetCtxs',
    }),
  });

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchUserSpotBalance = async (address: string) => {
  const response = await fetch(`${ENDPOINTS.HYPERLIQUID_L1}/info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      type: 'spotClearinghouseState',
      user: address,
    }),
  });

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchUserPerpsBalance = async (address: string) => {
  const response = await fetch(`${ENDPOINTS.HYPERLIQUID_L1}/info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      type: 'clearinghouseState',
      user: address,
      dex: '',
    }),
  });

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchSpotTokenDetails = async (tokenId: string): Promise<SpotTokenDetails> => {
  const response = await fetch(`${ENDPOINTS.HYPERLIQUID_L1}/info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      type: 'tokenDetails',
      tokenId,
    }),
  });

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};


export const fetchHLCandleSnapshot = async (coinId: string, timeFrame: '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all', tokenLaunchDate?: number) => {

  const timeRange = calculateCandleTimeRange(timeFrame, tokenLaunchDate);

  if (!timeRange) return null;

  const response = await fetch(`${ENDPOINTS.HYPERLIQUID_L1}/info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      type: 'candleSnapshot',
      req: {
        coin: coinId,
        interval: timeRange.interval,
        startTime: timeRange.startTime,
        endTime: timeRange.endTime,
      }
    }),
  });

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
}





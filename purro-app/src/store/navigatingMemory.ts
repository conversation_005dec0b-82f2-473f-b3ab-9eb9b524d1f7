import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface NavigatingMemoryState {
    walletPageMemory: string;
    marketPageMemory: string;
}

interface NavigatingMemoryProps {
    setWalletPageMemory: (walletPageMemory: string) => void;
    setMarketPageMemory: (marketPageMemory: string) => void;
}

const useNavigatingMemory = create<NavigatingMemoryProps & NavigatingMemoryState>()(
    persist(
        set => ({
            walletPageMemory: '',
            marketPageMemory: '',
            setWalletPageMemory: walletPageMemory => set({ walletPageMemory }),
            setMarketPageMemory: marketPageMemory => set({ marketPageMemory }),
        }),
        {
            name: 'navigating-memory-storage',
            partialize: state => ({ walletPageMemory: state.walletPageMemory, marketPageMemory: state.marketPageMemory }),
        }
    )
);

export default useNavigatingMemory;

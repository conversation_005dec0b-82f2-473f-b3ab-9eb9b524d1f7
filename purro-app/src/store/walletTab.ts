import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

export type WalletTab = "spot" | "perps" | "evm" | "nft";

interface State {
    currentTab: WalletTab;
}

interface Action {
    setCurrentTab: (tab: WalletTab) => void;
}

type WalletTabState = State & Action;

const persistOptions: PersistOptions<WalletTabState, Pick<State, 'currentTab'>> = {
    name: "wallet-tab-storage",
    partialize: (state) => ({ currentTab: state.currentTab }),
};

const useWalletTabStore = create<WalletTabState>()(
    persist(
        (set) => ({
            currentTab: "spot",
            setCurrentTab: (tab) => set({ currentTab: tab })
        }),
        persistOptions
    )
);

export default useWalletTabStore;
export interface GeckoTerminalTokenDataResponse {
  data: {
    id: string;
    type: string;
    attributes: {
      token_prices: {
        [tokenAddress: string]: string;
      };
      market_cap_usd: {
        [tokenAddress: string]: string;
      };
      h24_volume_usd: {
        [tokenAddress: string]: string;
      };
      h24_price_change_percentage: {
        [tokenAddress: string]: string;
      };
      total_reserve_in_usd: {
        [tokenAddress: string]: string;
      };
    };
  };
}

export interface HyperScanTokenBalanceResponse {
  token: {
    address: string;
    circulating_market_cap: string | null;
    decimals: string;
    exchange_rate: string | null;
    holders: string;
    icon_url: string | null;
    name: string;
    symbol: string;
    total_supply: string;
    type: string;
    volume_24h: string | null;
  };
  token_id: string | null;
  token_instance: string | null;
  value: string;
}

export interface HyperScanNftNextPageParams {
  items_count: number,
  token_contract_address_hash: string,
  token_id: string,
  token_type: string
}

export interface HyperScanNFTResponse {
  items: {
    is_unique: boolean;
    id: string;
    holder_address_hash: string;
    image_url: string;
    animation_url: string;
    external_app_url: string;
    metadata: {
      year: number;
      tags: string[];
      name: string;
      image_url: string;
      home_url: string;
      external_url: string;
      description: string;
      attributes: Array<{
        value: string;
        trait_type: string;
      }>;
    };
    token: {
      circulating_market_cap: string;
      icon_url: string;
      name: string;
      decimals: string;
      symbol: string;
      address: string;
      type: string;
      holders: string;
      exchange_rate: string;
      total_supply: string;
    };
    token_type: string;
    value: string;
  }[],
  next_page_params: HyperScanNftNextPageParams
}

export interface HyperScanNftCollectionsResponse {
  items: {
    token: {
      circulating_market_cap: string;
      icon_url: string;
      name: string;
      decimals: string;
      symbol: string;
      address: string;
      type: string;
      holders: string;
      exchange_rate: string;
      total_supply: string;
    },
    amount: string,
    token_instances: {
      is_unique: boolean,
      id: string,
      holder_address_hash: string,
      image_url: string,
      animation_url: string,
      external_app_url: string,
      metadata: {
        year: number,
        tags: string[],
        name: string,
        image_url: string,
        home_url: string,
        external_url: string,
        description: string,
        attributes: Array<{
          value: string;
          trait_type: string;
        }>
      }
    }[]
  }[]
  next_page_params: {
    token_contract_address_hash: string,
    token_type: string
  }
}

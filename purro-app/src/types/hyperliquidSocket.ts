// types.ts
/**
 * Type definitions cho Hyperliquid WebSocket API
 */

// Loại mạng được hỗ trợ
export type Network = 'mainnet' | 'testnet';

// Phương thức sử dụng trong tin nhắn WebSocket
export type WebSocketMethod = 'subscribe' | 'unsubscribe';

// Loại subscription có sẵn
export type SubscriptionType =
    | 'allMids'
    | 'notification'
    | 'webData2'
    | 'candle'
    | 'l2Book'
    | 'trades'
    | 'orderUpdates'
    | 'userEvents'
    | 'userFills'
    | 'userFundings'
    | 'userNonFundingLedgerUpdates'
    | 'activeAssetCtx'
    | 'activeAssetData'
    | 'userTwapSliceFills'
    | 'userTwapHistory'
    | 'bbo';

// Base subscription interface
export interface BaseSubscription {
    type: SubscriptionType;
}

// AllMids subscription
export interface AllMidsSubscription extends BaseSubscription {
    type: 'allMids';
}

// Notification subscription
export interface NotificationSubscription extends BaseSubscription {
    type: 'notification';
    user: string;
}

// L2Book subscription
export interface L2BookSubscription extends BaseSubscription {
    type: 'l2Book';
    coin: string;
    nSigFigs?: number;
    mantissa?: number;
}

// Trades subscription
export interface TradesSubscription extends BaseSubscription {
    type: 'trades';
    coin: string;
}

// Candle subscription
export interface CandleSubscription extends BaseSubscription {
    type: 'candle';
    coin: string;
    interval: string;
}

// Union type of all subscriptions
export type Subscription =
    | AllMidsSubscription
    | NotificationSubscription
    | L2BookSubscription
    | TradesSubscription
    | CandleSubscription
// Thêm các loại subscription khác khi cần

// WebSocket message để subscribe
export interface SubscribeMessage {
    method: 'subscribe';
    subscription: Subscription;
}

// WebSocket message để unsubscribe
export interface UnsubscribeMessage {
    method: 'unsubscribe';
    subscription: Subscription;
}

// Union type các tin nhắn có thể gửi đi
export type OutgoingMessage = SubscribeMessage | UnsubscribeMessage;

// Trạng thái kết nối WebSocket
export interface ConnectionState {
    status: 'connected' | 'connecting' | 'disconnected' | 'error';
    error: string | null;
}

// AllMids response data
export interface AllMidsData {
    mids: Record<string, string>;
}

// AllMids data mở rộng với trạng thái kết nối
export interface AllMidsWithConnection extends AllMidsData {
    isConnected: boolean;
}

// L2Book Level
export interface Level {
    px: string; // price
    sz: string; // size
    n: number;  // number of orders
}

// L2Book data
export interface L2BookData {
    coin: string;
    levels: [Level[], Level[]]; // [bids, asks]
    time: number;
}

// Trade data
export interface Trade {
    coin: string;
    side: string;
    px: string;
    sz: string;
    hash: string;
    time: number;
    tid: number;
    users: [string, string]; // [buyer, seller]
}

// Candle data
export interface Candle {
    t: number;   // open millis
    T: number;   // close millis
    s: string;   // coin
    i: string;   // interval
    o: number;   // open price
    c: number;   // close price
    h: number;   // high price
    l: number;   // low price
    v: number;   // volume (base unit)
    n: number;   // number of trades
}

// Base WebSocket response message
export interface BaseWebSocketMessage {
    channel: string;
}

// AllMids response message
export interface AllMidsMessage extends BaseWebSocketMessage {
    channel: 'allMids';
    data: AllMidsData;
}

// L2Book response message
export interface L2BookMessage extends BaseWebSocketMessage {
    channel: 'l2Book';
    data: L2BookData;
}

// Trades response message
export interface TradesMessage extends BaseWebSocketMessage {
    channel: 'trades';
    data: Trade[];
}

// Candle response message
export interface CandleMessage extends BaseWebSocketMessage {
    channel: 'candle';
    data: Candle;
}

// Subscription response message
export interface SubscriptionResponseMessage extends BaseWebSocketMessage {
    channel: 'subscriptionResponse';
    data: {
        subscription: Subscription;
    };
}

// Union type of all incoming WebSocket messages
export type WebSocketMessage =
    | AllMidsMessage
    | L2BookMessage
    | TradesMessage
    | CandleMessage
    | SubscriptionResponseMessage
    // Thêm các loại tin nhắn khác khi cần
    | BaseWebSocketMessage; // Fallback for unknown message types
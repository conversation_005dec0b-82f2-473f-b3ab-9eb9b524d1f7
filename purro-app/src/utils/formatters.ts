/**
 * Format a number as currency with thousands separators
 * @param value - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @param currency - Currency symbol (default: '$')
 * @returns Formatted currency string
 */
export function formatCurrency(value: number | undefined, decimals = 2, currency = '$'): string {
  if (value === undefined || isNaN(value)) {
    return `${currency}0.00`;
  }

  // Format with thousands separator and fixed decimal places
  const formattedValue = value.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  return `${currency}${formattedValue}`;
}

export const getGeckoTerminalName = (name: string) => {
  const parts = name.split('/');
  if (parts.length > 0) {
    return parts[0].trim();
  }

  return name;
};

export const formatBigAmount = (value: string): string => {
  const num = parseFloat(value);
  if (num >= 1000000000) {
    return `$${(num / 1000000000).toFixed(2)}B`;
  } else if (num >= 1000000) {
    return `$${(num / 1000000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `$${(num / 1000).toFixed(2)}K`;
  } else {
    return `$${num.toFixed(2)}`;
  }
};

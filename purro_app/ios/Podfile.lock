PODS:
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2

enum TransactionType {
  send,
  receive,
  swap,
  contract,
}

enum TransactionStatus {
  pending,
  confirmed,
  failed,
}

class TransactionModel {
  final String hash;
  final String from;
  final String to;
  final BigInt value;
  final BigInt gasPrice;
  final BigInt gasUsed;
  final TransactionType type;
  final TransactionStatus status;
  final DateTime timestamp;
  final int? blockNumber;
  final String? contractAddress;
  final String? tokenSymbol;
  final int? tokenDecimals;

  TransactionModel({
    required this.hash,
    required this.from,
    required this.to,
    required this.value,
    required this.gasPrice,
    required this.gasUsed,
    required this.type,
    required this.status,
    required this.timestamp,
    this.blockNumber,
    this.contractAddress,
    this.tokenSymbol,
    this.tokenDecimals,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      hash: json['hash'] as String,
      from: json['from'] as String,
      to: json['to'] as String,
      value: BigInt.parse(json['value'] as String),
      gasPrice: BigInt.parse(json['gasPrice'] as String),
      gasUsed: BigInt.parse(json['gasUsed'] as String),
      type: TransactionType.values[json['type'] as int],
      status: TransactionStatus.values[json['status'] as int],
      timestamp: DateTime.parse(json['timestamp'] as String),
      blockNumber: json['blockNumber'] as int?,
      contractAddress: json['contractAddress'] as String?,
      tokenSymbol: json['tokenSymbol'] as String?,
      tokenDecimals: json['tokenDecimals'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hash': hash,
      'from': from,
      'to': to,
      'value': value.toString(),
      'gasPrice': gasPrice.toString(),
      'gasUsed': gasUsed.toString(),
      'type': type.index,
      'status': status.index,
      'timestamp': timestamp.toIso8601String(),
      'blockNumber': blockNumber,
      'contractAddress': contractAddress,
      'tokenSymbol': tokenSymbol,
      'tokenDecimals': tokenDecimals,
    };
  }

  TransactionModel copyWith({
    String? hash,
    String? from,
    String? to,
    BigInt? value,
    BigInt? gasPrice,
    BigInt? gasUsed,
    TransactionType? type,
    TransactionStatus? status,
    DateTime? timestamp,
    int? blockNumber,
    String? contractAddress,
    String? tokenSymbol,
    int? tokenDecimals,
  }) {
    return TransactionModel(
      hash: hash ?? this.hash,
      from: from ?? this.from,
      to: to ?? this.to,
      value: value ?? this.value,
      gasPrice: gasPrice ?? this.gasPrice,
      gasUsed: gasUsed ?? this.gasUsed,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      blockNumber: blockNumber ?? this.blockNumber,
      contractAddress: contractAddress ?? this.contractAddress,
      tokenSymbol: tokenSymbol ?? this.tokenSymbol,
      tokenDecimals: tokenDecimals ?? this.tokenDecimals,
    );
  }

  String get formattedValue {
    final eth = value.toDouble() / 1e18;
    return eth.toStringAsFixed(4);
  }

  String get shortHash {
    if (hash.length <= 10) return hash;
    return '${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}';
  }

  String get shortFrom {
    if (from.length <= 10) return from;
    return '${from.substring(0, 6)}...${from.substring(from.length - 4)}';
  }

  String get shortTo {
    if (to.length <= 10) return to;
    return '${to.substring(0, 6)}...${to.substring(to.length - 4)}';
  }

  String get typeDisplayName {
    switch (type) {
      case TransactionType.send:
        return 'Send';
      case TransactionType.receive:
        return 'Receive';
      case TransactionType.swap:
        return 'Swap';
      case TransactionType.contract:
        return 'Contract';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.confirmed:
        return 'Confirmed';
      case TransactionStatus.failed:
        return 'Failed';
    }
  }

  @override
  String toString() {
    return 'TransactionModel(hash: $hash, from: $from, to: $to, value: $value, type: $type, status: $status, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel && other.hash == hash;
  }

  @override
  int get hashCode => hash.hashCode;
}

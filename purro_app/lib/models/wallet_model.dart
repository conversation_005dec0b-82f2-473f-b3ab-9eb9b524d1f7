import 'package:web3dart/web3dart.dart';

class WalletModel {
  final String address;
  final String? name;
  final BigInt balance;
  final DateTime createdAt;
  final DateTime lastUpdated;

  WalletModel({
    required this.address,
    this.name,
    required this.balance,
    required this.createdAt,
    required this.lastUpdated,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      address: json['address'] as String,
      name: json['name'] as String?,
      balance: BigInt.parse(json['balance'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'name': name,
      'balance': balance.toString(),
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  WalletModel copyWith({
    String? address,
    String? name,
    BigInt? balance,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return WalletModel(
      address: address ?? this.address,
      name: name ?? this.name,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  EthereumAddress get ethereumAddress => EthereumAddress.fromHex(address);

  String get formattedBalance {
    final eth = balance.toDouble() / 1e18;
    return eth.toStringAsFixed(4);
  }

  String get shortAddress {
    if (address.length <= 10) return address;
    return '${address.substring(0, 6)}...${address.substring(address.length - 4)}';
  }

  @override
  String toString() {
    return 'WalletModel(address: $address, name: $name, balance: $balance, createdAt: $createdAt, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WalletModel &&
        other.address == address &&
        other.name == name &&
        other.balance == balance &&
        other.createdAt == createdAt &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return address.hashCode ^
        name.hashCode ^
        balance.hashCode ^
        createdAt.hashCode ^
        lastUpdated.hashCode;
  }
}

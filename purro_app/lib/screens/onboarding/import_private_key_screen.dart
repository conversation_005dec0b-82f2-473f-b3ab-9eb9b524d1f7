import 'package:flutter/material.dart';
import '../../services/wallet_service.dart';
import '../../utils/constants.dart';

class ImportPrivateKeyScreen extends StatefulWidget {
  const ImportPrivateKeyScreen({super.key});

  @override
  State<ImportPrivateKeyScreen> createState() => _ImportPrivateKeyScreenState();
}

class _ImportPrivateKeyScreenState extends State<ImportPrivateKeyScreen> {
  final TextEditingController _privateKeyController = TextEditingController();
  final TextEditingController _walletNameController = TextEditingController();
  bool _isLoading = false;
  bool _isPrivateKeyVisible = false;

  @override
  void dispose() {
    _privateKeyController.dispose();
    _walletNameController.dispose();
    super.dispose();
  }

  Future<void> _importWallet() async {
    final privateKey = _privateKeyController.text.trim();
    final walletName = _walletNameController.text.trim();

    if (privateKey.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a private key'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!_isValidPrivateKey(privateKey)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid private key format'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final walletService = WalletService();
      final wallet = await walletService.importWalletFromPrivateKey(
        privateKey,
        name: walletName.isNotEmpty ? walletName : 'Imported Wallet'
      );

      if (mounted) {
        Navigator.pop(context, wallet);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to import wallet: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _isValidPrivateKey(String privateKey) {
    // Remove 0x prefix if present
    String cleanKey = privateKey.startsWith('0x') ? privateKey.substring(2) : privateKey;
    
    // Check if it's 64 characters long and contains only hex characters
    return cleanKey.length == 64 && RegExp(r'^[0-9a-fA-F]+$').hasMatch(cleanKey);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Import Private Key'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Import Wallet',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Enter your private key to import an existing wallet.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 32),
              
              // Warning card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red.shade600),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Never share your private key with anyone! Anyone with your private key can access your funds.',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Wallet name input
              TextField(
                controller: _walletNameController,
                decoration: InputDecoration(
                  labelText: 'Wallet Name (Optional)',
                  hintText: 'Enter wallet name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  ),
                  prefixIcon: const Icon(Icons.account_balance_wallet),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Private key input
              TextField(
                controller: _privateKeyController,
                obscureText: !_isPrivateKeyVisible,
                maxLines: _isPrivateKeyVisible ? 3 : 1,
                decoration: InputDecoration(
                  labelText: 'Private Key',
                  hintText: 'Enter your private key (64 characters)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  ),
                  prefixIcon: const Icon(Icons.key),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isPrivateKeyVisible ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPrivateKeyVisible = !_isPrivateKeyVisible;
                      });
                    },
                  ),
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Private key should be 64 characters long (with or without 0x prefix)',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              
              const Spacer(),
              
              // Import button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _importWallet,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(AppConstants.primaryColorValue),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    ),
                    disabledBackgroundColor: Colors.grey.shade300,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Import Wallet',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

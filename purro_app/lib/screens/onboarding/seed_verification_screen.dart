import 'package:flutter/material.dart';
import '../../services/wallet_service.dart';
import '../../utils/constants.dart';
import '../tabs/main_screen.dart';

class SeedVerificationScreen extends StatefulWidget {
  final String mnemonic;

  const SeedVerificationScreen({
    super.key,
    required this.mnemonic,
  });

  @override
  State<SeedVerificationScreen> createState() => _SeedVerificationScreenState();
}

class _SeedVerificationScreenState extends State<SeedVerificationScreen> {
  late List<String> _originalWords;
  late List<String> _shuffledWords;
  List<String> _selectedWords = [];
  List<int> _verificationPositions = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _setupVerification();
  }

  void _setupVerification() {
    _originalWords = widget.mnemonic.split(' ');
    
    // Select 4 random positions for verification
    final positions = List.generate(_originalWords.length, (index) => index);
    positions.shuffle();
    _verificationPositions = positions.take(4).toList()..sort();
    
    // Create shuffled words for selection
    final wordsToVerify = _verificationPositions.map((pos) => _originalWords[pos]).toList();
    _shuffledWords = List.from(wordsToVerify)..shuffle();
    
    // Add some random words to make it more challenging
    final allWords = widget.mnemonic.split(' ');
    final randomWords = <String>[];
    for (int i = 0; i < 4; i++) {
      String randomWord;
      do {
        randomWord = allWords[DateTime.now().millisecondsSinceEpoch % allWords.length];
      } while (wordsToVerify.contains(randomWord) || randomWords.contains(randomWord));
      randomWords.add(randomWord);
    }
    
    _shuffledWords.addAll(randomWords);
    _shuffledWords.shuffle();
    
    setState(() {});
  }

  void _selectWord(String word) {
    if (_selectedWords.length < _verificationPositions.length) {
      setState(() {
        _selectedWords.add(word);
      });
    }
  }

  void _removeWord(int index) {
    setState(() {
      _selectedWords.removeAt(index);
    });
  }

  bool _isVerificationCorrect() {
    if (_selectedWords.length != _verificationPositions.length) {
      return false;
    }
    
    for (int i = 0; i < _verificationPositions.length; i++) {
      if (_selectedWords[i] != _originalWords[_verificationPositions[i]]) {
        return false;
      }
    }
    
    return true;
  }

  Future<void> _completeVerification() async {
    if (!_isVerificationCorrect()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Verification failed. Please check the word order.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final walletService = WalletService();
      await walletService.importWallet(widget.mnemonic, name: 'My Wallet');
      
      if (mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const MainScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create wallet: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Verify Seed Phrase'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Verify Your Seed Phrase',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Select the words in the correct order for positions: ${_verificationPositions.map((pos) => pos + 1).join(', ')}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 32),
              
              // Selected words display
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Selected Words:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        for (int i = 0; i < _verificationPositions.length; i++)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: i < _selectedWords.length
                                  ? const Color(AppConstants.primaryColorValue)
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: i < _selectedWords.length
                                    ? const Color(AppConstants.primaryColorValue)
                                    : Colors.grey.shade300,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  '${_verificationPositions[i] + 1}. ',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: i < _selectedWords.length
                                        ? Colors.white
                                        : Colors.grey,
                                  ),
                                ),
                                Text(
                                  i < _selectedWords.length
                                      ? _selectedWords[i]
                                      : '___',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: i < _selectedWords.length
                                        ? Colors.white
                                        : Colors.grey,
                                  ),
                                ),
                                if (i < _selectedWords.length) ...[
                                  const SizedBox(width: 4),
                                  GestureDetector(
                                    onTap: () => _removeWord(i),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Word selection grid
              const Text(
                'Select Words:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 3,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _shuffledWords.length,
                  itemBuilder: (context, index) {
                    final word = _shuffledWords[index];
                    final isSelected = _selectedWords.contains(word);
                    
                    return GestureDetector(
                      onTap: isSelected ? null : () => _selectWord(word),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.grey.shade300 : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected 
                                ? Colors.grey.shade400 
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            word,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: isSelected ? Colors.grey.shade600 : Colors.black,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Verify button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedWords.length == _verificationPositions.length && !_isLoading
                      ? _completeVerification
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(AppConstants.primaryColorValue),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    ),
                    disabledBackgroundColor: Colors.grey.shade300,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Verify & Create Wallet',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

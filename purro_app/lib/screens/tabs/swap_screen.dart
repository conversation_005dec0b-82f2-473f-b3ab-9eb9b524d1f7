import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../services/gecko_terminal_service.dart';

class SwapScreen extends StatefulWidget {
  const SwapScreen({super.key});

  @override
  State<SwapScreen> createState() => _SwapScreenState();
}

class _SwapScreenState extends State<SwapScreen> {
  final TextEditingController _fromAmountController = TextEditingController();
  final TextEditingController _toAmountController = TextEditingController();

  String _fromToken = 'HYPE';
  String _toToken = 'ETH';
  bool _isLoading = false;
  bool _isLoadingTrending = false;
  List<TrendingToken> _trendingTokens = [];

  final List<String> _availableTokens = ['HYPE', 'ETH', 'USDC', 'USDT'];

  @override
  void initState() {
    super.initState();
    _loadTrendingTokens();
  }

  @override
  void dispose() {
    _fromAmountController.dispose();
    _toAmountController.dispose();
    super.dispose();
  }

  Future<void> _loadTrendingTokens() async {
    setState(() {
      _isLoadingTrending = true;
    });

    try {
      final tokens = await GeckoTerminalService.getTrendingTokens(limit: 8);
      setState(() {
        _trendingTokens = tokens;
      });
    } catch (e) {
      // Handle error silently or show a message
      print('Error loading trending tokens: $e');
    } finally {
      setState(() {
        _isLoadingTrending = false;
      });
    }
  }

  void _swapTokens() {
    setState(() {
      final temp = _fromToken;
      _fromToken = _toToken;
      _toToken = temp;
      
      final tempAmount = _fromAmountController.text;
      _fromAmountController.text = _toAmountController.text;
      _toAmountController.text = tempAmount;
    });
  }

  Future<void> _performSwap() async {
    if (_fromAmountController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter an amount to swap'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate swap process
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Swap functionality coming soon!'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _selectTrendingToken(TrendingToken token) {
    setState(() {
      _fromToken = token.symbol;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected ${token.symbol} for swap'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  bool get hasSwapData => _fromAmountController.text.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Swap'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // Swap Card
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // From Token
                    _TokenInputCard(
                      label: 'From',
                      token: _fromToken,
                      controller: _fromAmountController,
                      availableTokens: _availableTokens,
                      onTokenChanged: (token) {
                        setState(() {
                          _fromToken = token;
                        });
                      },
                      onAmountChanged: (amount) {
                        // Calculate estimated output
                        if (amount.isNotEmpty) {
                          try {
                            final inputAmount = double.parse(amount);
                            // Simulate exchange rate (1:1 for demo)
                            _toAmountController.text = inputAmount.toString();
                          } catch (e) {
                            _toAmountController.clear();
                          }
                        } else {
                          _toAmountController.clear();
                        }
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Swap Button
                    GestureDetector(
                      onTap: _swapTokens,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(AppConstants.primaryColorValue).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(AppConstants.primaryColorValue).withOpacity(0.3),
                          ),
                        ),
                        child: Icon(
                          Icons.swap_vert,
                          color: const Color(AppConstants.primaryColorValue),
                          size: 24,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // To Token
                    _TokenInputCard(
                      label: 'To',
                      token: _toToken,
                      controller: _toAmountController,
                      availableTokens: _availableTokens,
                      readOnly: true,
                      onTokenChanged: (token) {
                        setState(() {
                          _toToken = token;
                        });
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Swap Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _InfoRow('Exchange Rate', '1 $_fromToken = 1 $_toToken'),
                    const SizedBox(height: 8),
                    _InfoRow('Network Fee', '~0.001 HYPE'),
                    const SizedBox(height: 8),
                    _InfoRow('Slippage Tolerance', '0.5%'),
                  ],
                ),
              ),
              
              // Show trending tokens if no swap data, otherwise show swap button
              if (!hasSwapData) ...[
                // Trending Tokens Section
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Trending Tokens 24h',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (_isLoadingTrending)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      if (_isLoadingTrending)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: CircularProgressIndicator(),
                          ),
                        )
                      else if (_trendingTokens.isEmpty)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: Text(
                              'No trending tokens available',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ),
                        )
                      else
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _trendingTokens.length,
                          separatorBuilder: (context, index) => const SizedBox(height: 8),
                          itemBuilder: (context, index) {
                            final token = _trendingTokens[index];
                            return _TrendingTokenCard(
                              token: token,
                              onTap: () => _selectTrendingToken(token),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ] else ...[
                const Spacer(),

                // Swap Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _performSwap,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(AppConstants.primaryColorValue),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                      ),
                      disabledBackgroundColor: Colors.grey.shade300,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Swap Tokens',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _TokenInputCard extends StatelessWidget {
  final String label;
  final String token;
  final TextEditingController controller;
  final List<String> availableTokens;
  final bool readOnly;
  final Function(String) onTokenChanged;
  final Function(String)? onAmountChanged;

  const _TokenInputCard({
    required this.label,
    required this.token,
    required this.controller,
    required this.availableTokens,
    required this.onTokenChanged,
    this.readOnly = false,
    this.onAmountChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  readOnly: readOnly,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
                  decoration: const InputDecoration(
                    hintText: '0.0',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: onAmountChanged,
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () {
                  _showTokenSelector(context);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        token,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showTokenSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Token',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ...availableTokens.map((tokenOption) {
                return ListTile(
                  title: Text(tokenOption),
                  onTap: () {
                    onTokenChanged(tokenOption);
                    Navigator.pop(context);
                  },
                  selected: tokenOption == token,
                );
              }).toList(),
            ],
          ),
        );
      },
    );
  }
}

class _TrendingTokenCard extends StatelessWidget {
  final TrendingToken token;
  final VoidCallback onTap;

  const _TrendingTokenCard({
    required this.token,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Token Icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(AppConstants.primaryColorValue).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: token.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.network(
                          token.imageUrl!,
                          width: 32,
                          height: 32,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.currency_exchange,
                              color: const Color(AppConstants.primaryColorValue),
                              size: 16,
                            );
                          },
                        ),
                      )
                    : Icon(
                        Icons.currency_exchange,
                        color: const Color(AppConstants.primaryColorValue),
                        size: 16,
                      ),
              ),

              const SizedBox(width: 12),

              // Token Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          token.symbol,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: token.isPriceUp
                                ? const Color(AppConstants.successColorValue).withOpacity(0.1)
                                : const Color(AppConstants.errorColorValue).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            token.formattedPriceChange,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: token.isPriceUp
                                  ? const Color(AppConstants.successColorValue)
                                  : const Color(AppConstants.errorColorValue),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      token.name,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Price and Volume
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    token.formattedPrice,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Vol: ${token.formattedVolume}',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

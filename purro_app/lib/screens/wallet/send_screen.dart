import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/wallet_service.dart';
import '../../utils/constants.dart';
import '../../utils/crypto_utils.dart';

class SendScreen extends StatefulWidget {
  const SendScreen({super.key});

  @override
  State<SendScreen> createState() => _SendScreenState();
}

class _SendScreenState extends State<SendScreen> {
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final WalletService _walletService = WalletService();
  
  bool _isLoading = false;
  BigInt _estimatedFee = BigInt.zero;
  bool _isCalculatingFee = false;

  @override
  void dispose() {
    _addressController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _calculateFee() async {
    final address = _addressController.text.trim();
    final amountText = _amountController.text.trim();

    if (address.isEmpty || amountText.isEmpty) {
      setState(() {
        _estimatedFee = BigInt.zero;
      });
      return;
    }

    try {
      final amount = double.parse(amountText);
      setState(() {
        _isCalculatingFee = true;
      });

      final fee = await _walletService.estimateTransactionFee(
        to: address,
        amount: amount,
      );

      setState(() {
        _estimatedFee = fee;
        _isCalculatingFee = false;
      });
    } catch (e) {
      setState(() {
        _estimatedFee = BigInt.zero;
        _isCalculatingFee = false;
      });
    }
  }

  Future<void> _sendTransaction() async {
    final address = _addressController.text.trim();
    final amountText = _amountController.text.trim();

    if (address.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter recipient address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!_walletService.isValidAddress(address)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid recipient address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (amountText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter amount to send'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    double amount;
    try {
      amount = double.parse(amountText);
      if (amount <= 0) {
        throw Exception('Amount must be greater than 0');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid amount'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Check if user has sufficient balance
    final wallet = _walletService.currentWallet;
    if (wallet != null) {
      final balance = CryptoUtils.weiToEth(wallet.balance);
      final totalCost = amount + CryptoUtils.weiToEth(_estimatedFee);
      
      if (totalCost > balance) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Insufficient balance for transaction and fees'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final txHash = await _walletService.sendTransaction(
        to: address,
        amount: amount,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Transaction sent! Hash: ${CryptoUtils.formatAddress(txHash)}'),
            backgroundColor: const Color(AppConstants.successColorValue),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send transaction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _pasteFromClipboard() async {
    final clipboardData = await Clipboard.getData('text/plain');
    if (clipboardData?.text != null) {
      _addressController.text = clipboardData!.text!;
      _calculateFee();
    }
  }

  void _setMaxAmount() {
    final wallet = _walletService.currentWallet;
    if (wallet != null) {
      final balance = CryptoUtils.weiToEth(wallet.balance);
      final feeInEth = CryptoUtils.weiToEth(_estimatedFee);
      final maxAmount = balance - feeInEth;
      
      if (maxAmount > 0) {
        _amountController.text = maxAmount.toStringAsFixed(6);
        _calculateFee();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final wallet = _walletService.currentWallet;
    
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Send HYPE'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // Balance card
              if (wallet != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Available Balance',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${CryptoUtils.formatBalance(wallet.balance)} ${AppConstants.currencySymbol}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 24),
              
              // Send form
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Recipient address
                      const Text(
                        'Recipient Address',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _addressController,
                        decoration: InputDecoration(
                          hintText: '0x...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.paste),
                                onPressed: _pasteFromClipboard,
                              ),
                              IconButton(
                                icon: const Icon(Icons.qr_code_scanner),
                                onPressed: () {
                                  // TODO: Implement QR scanner
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('QR Scanner coming soon!'),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        onChanged: (_) => _calculateFee(),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Amount',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextButton(
                            onPressed: _setMaxAmount,
                            child: const Text('MAX'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _amountController,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        decoration: InputDecoration(
                          hintText: '0.0',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixText: AppConstants.currencySymbol,
                        ),
                        onChanged: (_) => _calculateFee(),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Transaction details
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('Network Fee'),
                                _isCalculatingFee
                                    ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      )
                                    : Text(
                                        '${CryptoUtils.formatBalance(_estimatedFee)} ${AppConstants.currencySymbol}',
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                      ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('Total'),
                                Text(
                                  '${_getTotalAmount()} ${AppConstants.currencySymbol}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      const Spacer(),
                      
                      // Send button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _sendTransaction,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(AppConstants.primaryColorValue),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                            ),
                            disabledBackgroundColor: Colors.grey.shade300,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Send Transaction',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalAmount() {
    try {
      final amount = double.parse(_amountController.text.isEmpty ? '0' : _amountController.text);
      final fee = CryptoUtils.weiToEth(_estimatedFee);
      return (amount + fee).toStringAsFixed(6);
    } catch (e) {
      return '0.000000';
    }
  }
}

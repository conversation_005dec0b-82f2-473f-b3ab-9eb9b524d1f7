import 'dart:typed_data';
import 'package:web3dart/web3dart.dart';
import 'package:http/http.dart' as http;
import '../utils/constants.dart';
import '../models/transaction_model.dart';

class BlockchainService {
  late Web3Client _client;
  late String _rpcUrl;
  late int _chainId;

  BlockchainService() {
    _rpcUrl = AppConstants.rpcUrl;
    _chainId = AppConstants.chainId;
    _client = Web3Client(_rpcUrl, http.Client());
  }

  Web3Client get client => _client;
  String get rpcUrl => _rpcUrl;
  int get chainId => _chainId;

  /// Get balance for an address
  Future<BigInt> getBalance(String address) async {
    try {
      final ethereumAddress = EthereumAddress.fromHex(address);
      final balance = await _client.getBalance(ethereumAddress);
      return balance.getInWei;
    } catch (e) {
      throw Exception('Failed to get balance: $e');
    }
  }

  /// Get current gas price
  Future<BigInt> getGasPrice() async {
    try {
      final gasPrice = await _client.getGasPrice();
      return gasPrice.getInWei;
    } catch (e) {
      throw Exception('Failed to get gas price: $e');
    }
  }

  /// Get transaction count (nonce) for an address
  Future<int> getTransactionCount(String address) async {
    try {
      final ethereumAddress = EthereumAddress.fromHex(address);
      return await _client.getTransactionCount(ethereumAddress);
    } catch (e) {
      throw Exception('Failed to get transaction count: $e');
    }
  }

  /// Send transaction
  Future<String> sendTransaction({
    required EthPrivateKey credentials,
    required String to,
    required BigInt value,
    BigInt? gasPrice,
    int? gasLimit,
    Uint8List? data,
  }) async {
    try {
      final transaction = Transaction(
        to: EthereumAddress.fromHex(to),
        value: EtherAmount.inWei(value),
        gasPrice: gasPrice != null ? EtherAmount.inWei(gasPrice) : null,
        maxGas: gasLimit,
        data: data,
      );

      final txHash = await _client.sendTransaction(
        credentials,
        transaction,
        chainId: _chainId,
      );

      return txHash;
    } catch (e) {
      throw Exception('Failed to send transaction: $e');
    }
  }

  /// Get transaction by hash
  Future<TransactionInformation?> getTransaction(String hash) async {
    try {
      return await _client.getTransactionByHash(hash);
    } catch (e) {
      throw Exception('Failed to get transaction: $e');
    }
  }

  /// Get transaction receipt
  Future<TransactionReceipt?> getTransactionReceipt(String hash) async {
    try {
      return await _client.getTransactionReceipt(hash);
    } catch (e) {
      throw Exception('Failed to get transaction receipt: $e');
    }
  }

  /// Get current block number
  Future<int> getBlockNumber() async {
    try {
      return await _client.getBlockNumber();
    } catch (e) {
      throw Exception('Failed to get block number: $e');
    }
  }

  /// Estimate gas for a transaction
  Future<BigInt> estimateGas({
    required String from,
    required String to,
    required BigInt value,
    Uint8List? data,
  }) async {
    try {
      final gasEstimate = await _client.estimateGas(
        sender: EthereumAddress.fromHex(from),
        to: EthereumAddress.fromHex(to),
        value: EtherAmount.inWei(value),
        data: data,
      );
      return gasEstimate;
    } catch (e) {
      throw Exception('Failed to estimate gas: $e');
    }
  }

  /// Get transaction history for an address (simplified version)
  /// In a real implementation, you would use an indexer service
  Future<List<TransactionModel>> getTransactionHistory(String address) async {
    try {
      // This is a simplified implementation
      // In production, you would use services like Etherscan API or The Graph
      final List<TransactionModel> transactions = [];
      
      // For now, return empty list
      // You can implement actual transaction fetching using block scanning
      // or external APIs when available for Hyperliquid
      
      return transactions;
    } catch (e) {
      throw Exception('Failed to get transaction history: $e');
    }
  }

  /// Check if address is valid
  bool isValidAddress(String address) {
    try {
      EthereumAddress.fromHex(address);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get network info
  Map<String, dynamic> getNetworkInfo() {
    return {
      'name': NetworkConstants.hyperliquidName,
      'rpcUrl': NetworkConstants.hyperliquidRpc,
      'chainId': NetworkConstants.hyperliquidChainId,
      'symbol': NetworkConstants.hyperliquidSymbol,
      'explorer': NetworkConstants.hyperliquidExplorer,
    };
  }

  /// Check network connectivity
  Future<bool> isNetworkConnected() async {
    try {
      await getBlockNumber();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get latest block
  Future<BlockInformation> getLatestBlock() async {
    try {
      return await _client.getBlockInformation();
    } catch (e) {
      throw Exception('Failed to get latest block: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _client.dispose();
  }
}

import 'dart:convert';
import 'package:http/http.dart' as http;

class GeckoTerminalService {
  static const String _baseUrl = 'https://api.geckoterminal.com/api/v2';
  
  /// Get trending tokens for the last 24 hours
  static Future<List<TrendingToken>> getTrendingTokens({int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/networks/trending_pools'),
        headers: {
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final pools = data['data'] as List;
        
        return pools.take(limit).map((pool) => TrendingToken.fromJson(pool)).toList();
      } else {
        throw Exception('Failed to load trending tokens: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching trending tokens: $e');
    }
  }

  /// Get trending tokens for a specific network
  static Future<List<TrendingToken>> getTrendingTokensByNetwork(String networkId, {int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/networks/$networkId/trending_pools'),
        headers: {
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final pools = data['data'] as List;
        
        return pools.take(limit).map((pool) => TrendingToken.fromJson(pool)).toList();
      } else {
        throw Exception('Failed to load trending tokens: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching trending tokens: $e');
    }
  }

  /// Search tokens by query
  static Future<List<TrendingToken>> searchTokens(String query, {int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search/pools?query=$query&limit=$limit'),
        headers: {
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final pools = data['data'] as List;
        
        return pools.map((pool) => TrendingToken.fromJson(pool)).toList();
      } else {
        throw Exception('Failed to search tokens: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error searching tokens: $e');
    }
  }
}

class TrendingToken {
  final String id;
  final String name;
  final String symbol;
  final String? imageUrl;
  final double priceUsd;
  final double priceChange24h;
  final double volume24h;
  final String network;
  final String address;

  TrendingToken({
    required this.id,
    required this.name,
    required this.symbol,
    this.imageUrl,
    required this.priceUsd,
    required this.priceChange24h,
    required this.volume24h,
    required this.network,
    required this.address,
  });

  factory TrendingToken.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>;
    final relationships = json['relationships'] as Map<String, dynamic>?;
    
    // Extract token info
    final baseToken = attributes['base_token'] as Map<String, dynamic>?;
    final quoteToken = attributes['quote_token'] as Map<String, dynamic>?;
    
    // Use base token as the main token
    final tokenName = baseToken?['name'] ?? 'Unknown Token';
    final tokenSymbol = baseToken?['symbol'] ?? 'UNKNOWN';
    final tokenAddress = baseToken?['address'] ?? '';
    final tokenImage = baseToken?['image_url'];
    
    // Price and volume data
    final priceUsd = double.tryParse(attributes['price_usd']?.toString() ?? '0') ?? 0.0;
    final priceChange24h = double.tryParse(attributes['price_change_percentage']?['h24']?.toString() ?? '0') ?? 0.0;
    final volume24h = double.tryParse(attributes['volume_usd']?['h24']?.toString() ?? '0') ?? 0.0;
    
    // Network info
    final networkData = relationships?['network']?['data'] as Map<String, dynamic>?;
    final networkId = networkData?['id'] ?? 'unknown';

    return TrendingToken(
      id: json['id'] ?? '',
      name: tokenName,
      symbol: tokenSymbol,
      imageUrl: tokenImage,
      priceUsd: priceUsd,
      priceChange24h: priceChange24h,
      volume24h: volume24h,
      network: networkId,
      address: tokenAddress,
    );
  }

  String get formattedPrice {
    if (priceUsd >= 1) {
      return '\$${priceUsd.toStringAsFixed(2)}';
    } else if (priceUsd >= 0.01) {
      return '\$${priceUsd.toStringAsFixed(4)}';
    } else {
      return '\$${priceUsd.toStringAsExponential(2)}';
    }
  }

  String get formattedPriceChange {
    final sign = priceChange24h >= 0 ? '+' : '';
    return '$sign${priceChange24h.toStringAsFixed(2)}%';
  }

  String get formattedVolume {
    if (volume24h >= 1000000) {
      return '\$${(volume24h / 1000000).toStringAsFixed(1)}M';
    } else if (volume24h >= 1000) {
      return '\$${(volume24h / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${volume24h.toStringAsFixed(0)}';
    }
  }

  bool get isPriceUp => priceChange24h >= 0;
}

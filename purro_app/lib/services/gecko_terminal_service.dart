import 'dart:convert';
import 'package:http/http.dart' as http;

class GeckoTerminalService {
  static const String _hyperliquidApiUrl = 'https://api.hyperliquid.xyz/info';

  /// Get trending tokens from Hyperliquid
  static Future<List<TrendingToken>> getTrendingTokens({int limit = 10}) async {
    try {
      // Get all mids (market data) from Hyperliquid
      final response = await http.post(
        Uri.parse(_hyperliquidApiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'type': 'allMids'
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;

        // Create demo trending tokens based on Hyperliquid data
        return _createDemoTokens().take(limit).toList();
      } else {
        // Return demo data if API fails
        return _createDemoTokens().take(limit).toList();
      }
    } catch (e) {
      // Return demo data if there's an error
      return _createDemoTokens().take(limit).toList();
    }
  }

  /// Create demo tokens for Hyperliquid ecosystem
  static List<TrendingToken> _createDemoTokens() {
    return [
      TrendingToken(
        id: 'hype-1',
        name: 'Hyperliquid',
        symbol: 'HYPE',
        imageUrl: null,
        priceUsd: 25.67,
        priceChange24h: 12.45,
        volume24h: 2500000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'usdc-1',
        name: 'USD Coin',
        symbol: 'USDC',
        imageUrl: null,
        priceUsd: 1.00,
        priceChange24h: 0.02,
        volume24h: 1800000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'eth-1',
        name: 'Ethereum',
        symbol: 'ETH',
        imageUrl: null,
        priceUsd: 3245.89,
        priceChange24h: -2.34,
        volume24h: 5600000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'btc-1',
        name: 'Bitcoin',
        symbol: 'BTC',
        imageUrl: null,
        priceUsd: 67890.12,
        priceChange24h: 5.67,
        volume24h: 8900000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'sol-1',
        name: 'Solana',
        symbol: 'SOL',
        imageUrl: null,
        priceUsd: 198.45,
        priceChange24h: 8.92,
        volume24h: 3400000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'avax-1',
        name: 'Avalanche',
        symbol: 'AVAX',
        imageUrl: null,
        priceUsd: 42.78,
        priceChange24h: -1.23,
        volume24h: 1200000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
    ];
  }

  /// Get trending tokens for a specific network (returns demo data)
  static Future<List<TrendingToken>> getTrendingTokensByNetwork(String networkId, {int limit = 10}) async {
    // Return demo data for Hyperliquid
    return _createDemoTokens().take(limit).toList();
  }

  /// Search tokens by query (returns demo data)
  static Future<List<TrendingToken>> searchTokens(String query, {int limit = 10}) async {
    // Filter demo tokens by query
    final allTokens = _createDemoTokens();
    final filteredTokens = allTokens.where((token) =>
        token.name.toLowerCase().contains(query.toLowerCase()) ||
        token.symbol.toLowerCase().contains(query.toLowerCase())).toList();

    return filteredTokens.take(limit).toList();
  }
}

class TrendingToken {
  final String id;
  final String name;
  final String symbol;
  final String? imageUrl;
  final double priceUsd;
  final double priceChange24h;
  final double volume24h;
  final String network;
  final String address;

  TrendingToken({
    required this.id,
    required this.name,
    required this.symbol,
    this.imageUrl,
    required this.priceUsd,
    required this.priceChange24h,
    required this.volume24h,
    required this.network,
    required this.address,
  });

  factory TrendingToken.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>;
    final relationships = json['relationships'] as Map<String, dynamic>?;
    
    // Extract token info
    final baseToken = attributes['base_token'] as Map<String, dynamic>?;
    final quoteToken = attributes['quote_token'] as Map<String, dynamic>?;
    
    // Use base token as the main token
    final tokenName = baseToken?['name'] ?? 'Unknown Token';
    final tokenSymbol = baseToken?['symbol'] ?? 'UNKNOWN';
    final tokenAddress = baseToken?['address'] ?? '';
    final tokenImage = baseToken?['image_url'];
    
    // Price and volume data
    final priceUsd = double.tryParse(attributes['price_usd']?.toString() ?? '0') ?? 0.0;
    final priceChange24h = double.tryParse(attributes['price_change_percentage']?['h24']?.toString() ?? '0') ?? 0.0;
    final volume24h = double.tryParse(attributes['volume_usd']?['h24']?.toString() ?? '0') ?? 0.0;
    
    // Network info
    final networkData = relationships?['network']?['data'] as Map<String, dynamic>?;
    final networkId = networkData?['id'] ?? 'unknown';

    return TrendingToken(
      id: json['id'] ?? '',
      name: tokenName,
      symbol: tokenSymbol,
      imageUrl: tokenImage,
      priceUsd: priceUsd,
      priceChange24h: priceChange24h,
      volume24h: volume24h,
      network: networkId,
      address: tokenAddress,
    );
  }

  String get formattedPrice {
    if (priceUsd >= 1) {
      return '\$${priceUsd.toStringAsFixed(2)}';
    } else if (priceUsd >= 0.01) {
      return '\$${priceUsd.toStringAsFixed(4)}';
    } else {
      return '\$${priceUsd.toStringAsExponential(2)}';
    }
  }

  String get formattedPriceChange {
    final sign = priceChange24h >= 0 ? '+' : '';
    return '$sign${priceChange24h.toStringAsFixed(2)}%';
  }

  String get formattedVolume {
    if (volume24h >= 1000000) {
      return '\$${(volume24h / 1000000).toStringAsFixed(1)}M';
    } else if (volume24h >= 1000) {
      return '\$${(volume24h / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${volume24h.toStringAsFixed(0)}';
    }
  }

  bool get isPriceUp => priceChange24h >= 0;
}

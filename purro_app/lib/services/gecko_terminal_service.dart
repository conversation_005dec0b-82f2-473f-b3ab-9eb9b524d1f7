import 'dart:convert';
import 'package:http/http.dart' as http;

class GeckoTerminalService {
  static const String _baseUrl = 'https://api.geckoterminal.com/api/v2';

  /// Get trending tokens from Hyperliquid network
  static Future<List<TrendingToken>> getTrendingTokens({int limit = 10}) async {
    try {
      // Use hyperevm network for Hyperliquid EVM
      final response = await http.get(
        Uri.parse('$_baseUrl/networks/hyperevm/trending_pools?page=1&duration=24h'),
        headers: {
          'Accept': 'application/json;version=20230302',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('API Response: $data'); // Debug log

        final pools = data['data'] as List?;

        if (pools != null && pools.isNotEmpty) {
          print('Found ${pools.length} pools'); // Debug log
          return pools.take(limit).map((pool) => TrendingToken.fromJson(pool)).toList();
        } else {
          print('No pools found, using demo data'); // Debug log
          return _createDemoTokens().take(limit).toList();
        }
      } else {
        print('API failed with status: ${response.statusCode}'); // Debug log
        return _createDemoTokens().take(limit).toList();
      }
    } catch (e) {
      // Return demo data if there's an error
      print('Error fetching trending tokens: $e');
      return _createDemoTokens().take(limit).toList();
    }
  }

  /// Create demo tokens for Hyperliquid ecosystem
  static List<TrendingToken> _createDemoTokens() {
    return [
      TrendingToken(
        id: 'hype-1',
        name: 'Hyperliquid',
        symbol: 'HYPE',
        imageUrl: null,
        priceUsd: 25.67,
        priceChange24h: 12.45,
        volume24h: 2500000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'usdc-1',
        name: 'USD Coin',
        symbol: 'USDC',
        imageUrl: null,
        priceUsd: 1.00,
        priceChange24h: 0.02,
        volume24h: 1800000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'eth-1',
        name: 'Ethereum',
        symbol: 'ETH',
        imageUrl: null,
        priceUsd: 3245.89,
        priceChange24h: -2.34,
        volume24h: 5600000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'btc-1',
        name: 'Bitcoin',
        symbol: 'BTC',
        imageUrl: null,
        priceUsd: 67890.12,
        priceChange24h: 5.67,
        volume24h: 8900000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'sol-1',
        name: 'Solana',
        symbol: 'SOL',
        imageUrl: null,
        priceUsd: 198.45,
        priceChange24h: 8.92,
        volume24h: 3400000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
      TrendingToken(
        id: 'avax-1',
        name: 'Avalanche',
        symbol: 'AVAX',
        imageUrl: null,
        priceUsd: 42.78,
        priceChange24h: -1.23,
        volume24h: 1200000,
        network: 'hyperliquid',
        address: '******************************************',
      ),
    ];
  }

  /// Get trending tokens for a specific network
  static Future<List<TrendingToken>> getTrendingTokensByNetwork(String networkId, {int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/networks/$networkId/trending_pools?page=1&duration=24h'),
        headers: {
          'Accept': 'application/json;version=20230302',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final pools = data['data'] as List?;

        if (pools != null && pools.isNotEmpty) {
          return pools.take(limit).map((pool) => TrendingToken.fromJson(pool)).toList();
        }
      }
    } catch (e) {
      print('Error fetching trending tokens for network $networkId: $e');
    }

    // Return demo data as fallback
    return _createDemoTokens().take(limit).toList();
  }

  /// Search tokens by query
  static Future<List<TrendingToken>> searchTokens(String query, {int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search/pools?query=$query&limit=$limit'),
        headers: {
          'Accept': 'application/json;version=20230302',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final pools = data['data'] as List?;

        if (pools != null && pools.isNotEmpty) {
          return pools.map((pool) => TrendingToken.fromJson(pool)).toList();
        }
      }
    } catch (e) {
      print('Error searching tokens: $e');
    }

    // Filter demo tokens by query as fallback
    final allTokens = _createDemoTokens();
    final filteredTokens = allTokens.where((token) =>
        token.name.toLowerCase().contains(query.toLowerCase()) ||
        token.symbol.toLowerCase().contains(query.toLowerCase())).toList();

    return filteredTokens.take(limit).toList();
  }
}

class TrendingToken {
  final String id;
  final String name;
  final String symbol;
  final String? imageUrl;
  final double priceUsd;
  final double priceChange24h;
  final double volume24h;
  final String network;
  final String address;

  TrendingToken({
    required this.id,
    required this.name,
    required this.symbol,
    this.imageUrl,
    required this.priceUsd,
    required this.priceChange24h,
    required this.volume24h,
    required this.network,
    required this.address,
  });

  factory TrendingToken.fromJson(Map<String, dynamic> json) {
    try {
      print('Parsing pool: ${json['id']}'); // Debug log

      final attributes = json['attributes'] as Map<String, dynamic>? ?? {};
      final relationships = json['relationships'] as Map<String, dynamic>? ?? {};

      // Get pool name and extract token info from it
      final poolName = attributes['name']?.toString() ?? 'Unknown Pool';
      print('Pool name: $poolName'); // Debug log

      // Extract token symbols from pool name (e.g., "PURR / WHYPE 0.3%")
      String tokenSymbol = 'UNKNOWN';
      String tokenName = 'Unknown Token';

      if (poolName.contains('/')) {
        final parts = poolName.split('/');
        if (parts.isNotEmpty) {
          tokenSymbol = parts[0].trim();
          tokenName = tokenSymbol;
        }
      }

      // Try to get base token from relationships
      final baseTokenData = relationships['base_token']?['data'] as Map<String, dynamic>?;
      if (baseTokenData != null) {
        // We have base token reference, but need to get actual token data
        // For now, use the symbol from pool name
      }

      print('Token: $tokenSymbol ($tokenName)'); // Debug log

      // Price and volume data - use base token price
      final priceUsd = double.tryParse(attributes['base_token_price_usd']?.toString() ?? '0') ?? 0.0;

      // Handle price change percentage
      final priceChangeData = attributes['price_change_percentage'] as Map<String, dynamic>? ?? {};
      final priceChange24h = double.tryParse(priceChangeData['h24']?.toString() ?? '0') ?? 0.0;

      // Handle volume data
      final volumeData = attributes['volume_usd'] as Map<String, dynamic>? ?? {};
      final volume24h = double.tryParse(volumeData['h24']?.toString() ?? '0') ?? 0.0;

      // Pool address as token address
      final tokenAddress = attributes['address']?.toString() ?? '';

      // Network info
      final networkData = relationships['network']?['data'] as Map<String, dynamic>?;
      final networkId = networkData?['id']?.toString() ?? 'hyperevm';

      final token = TrendingToken(
        id: json['id']?.toString() ?? '',
        name: tokenName,
        symbol: tokenSymbol,
        imageUrl: null, // No image URL in this API response
        priceUsd: priceUsd,
        priceChange24h: priceChange24h,
        volume24h: volume24h,
        network: networkId,
        address: tokenAddress,
      );

      print('Created token: ${token.symbol} - ${token.formattedPrice} - ${token.formattedPriceChange}');
      return token;
    } catch (e) {
      print('Error parsing token: $e');
      // Return a fallback token
      return TrendingToken(
        id: json['id']?.toString() ?? 'unknown',
        name: 'Unknown Token',
        symbol: 'UNKNOWN',
        imageUrl: null,
        priceUsd: 0.0,
        priceChange24h: 0.0,
        volume24h: 0.0,
        network: 'unknown',
        address: '',
      );
    }
  }

  String get formattedPrice {
    if (priceUsd >= 1) {
      return '\$${priceUsd.toStringAsFixed(2)}';
    } else if (priceUsd >= 0.001) {
      return '\$${priceUsd.toStringAsFixed(4)}';
    } else if (priceUsd >= 0.000001) {
      return '\$${priceUsd.toStringAsFixed(6)}';
    } else if (priceUsd > 0) {
      return '\$${priceUsd.toStringAsExponential(2)}';
    } else {
      return '\$0.00';
    }
  }

  String get formattedPriceChange {
    final sign = priceChange24h >= 0 ? '+' : '';
    return '$sign${priceChange24h.toStringAsFixed(2)}%';
  }

  String get formattedVolume {
    if (volume24h >= 1000000) {
      return '\$${(volume24h / 1000000).toStringAsFixed(1)}M';
    } else if (volume24h >= 1000) {
      return '\$${(volume24h / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${volume24h.toStringAsFixed(0)}';
    }
  }

  bool get isPriceUp => priceChange24h >= 0;
}

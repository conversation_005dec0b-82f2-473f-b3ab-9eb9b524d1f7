import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/constants.dart';

class StorageService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Secure storage for sensitive data
  static Future<void> storeSecurely(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecurely(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> deleteSecurely(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecureStorage() async {
    await _secureStorage.deleteAll();
  }

  // Regular storage for non-sensitive data
  static Future<void> store(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  static Future<String?> get(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  static Future<void> storeBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  static Future<bool> getBool(String key, {bool defaultValue = false}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? defaultValue;
  }

  static Future<void> storeInt(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  static Future<int> getInt(String key, {int defaultValue = 0}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key) ?? defaultValue;
  }

  static Future<void> storeDouble(String key, double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(key, value);
  }

  static Future<double> getDouble(String key, {double defaultValue = 0.0}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(key) ?? defaultValue;
  }

  static Future<void> storeList(String key, List<String> value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(key, value);
  }

  static Future<List<String>> getList(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(key) ?? [];
  }

  static Future<void> storeJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await store(key, jsonString);
  }

  static Future<Map<String, dynamic>?> getJson(String key) async {
    final jsonString = await get(key);
    if (jsonString == null) return null;
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  static Future<void> delete(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  static Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Wallet specific methods
  static Future<void> storeSeedPhrase(String seedPhrase) async {
    await storeSecurely(AppConstants.seedPhraseKey, seedPhrase);
  }

  static Future<String?> getSeedPhrase() async {
    return await getSecurely(AppConstants.seedPhraseKey);
  }

  static Future<void> deleteSeedPhrase() async {
    await deleteSecurely(AppConstants.seedPhraseKey);
  }

  static Future<void> setWalletCreated(bool created) async {
    await storeBool(AppConstants.isWalletCreatedKey, created);
  }

  static Future<bool> isWalletCreated() async {
    return await getBool(AppConstants.isWalletCreatedKey);
  }

  static Future<void> storeWalletData(Map<String, dynamic> walletData) async {
    await storeJson(AppConstants.walletKey, walletData);
  }

  static Future<Map<String, dynamic>?> getWalletData() async {
    return await getJson(AppConstants.walletKey);
  }

  static Future<void> storeTransactionHistory(List<Map<String, dynamic>> transactions) async {
    final jsonString = jsonEncode(transactions);
    await store(AppConstants.transactionHistoryKey, jsonString);
  }

  static Future<List<Map<String, dynamic>>> getTransactionHistory() async {
    final jsonString = await get(AppConstants.transactionHistoryKey);
    if (jsonString == null) return [];
    try {
      final List<dynamic> decoded = jsonDecode(jsonString);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }
}

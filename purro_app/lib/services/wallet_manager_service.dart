import 'dart:convert';
import '../models/wallet_model.dart';
import '../utils/constants.dart';
import 'storage_service.dart';

class WalletManagerService {
  static final WalletManagerService _instance = WalletManagerService._internal();
  factory WalletManagerService() => _instance;
  WalletManagerService._internal();

  List<WalletModel> _wallets = [];
  int _currentWalletIndex = 0;

  List<WalletModel> get wallets => _wallets;
  WalletModel? get currentWallet => _wallets.isNotEmpty ? _wallets[_currentWalletIndex] : null;
  int get currentWalletIndex => _currentWalletIndex;

  /// Initialize wallet manager
  Future<void> initialize() async {
    await _loadWallets();
  }

  /// Load all wallets from storage
  Future<void> _loadWallets() async {
    try {
      final walletsJson = await StorageService.get('all_wallets');
      if (walletsJson != null) {
        final List<dynamic> walletsList = jsonDecode(walletsJson);
        _wallets = walletsList.map((json) => WalletModel.fromJson(json)).toList();
      }
      
      final currentIndex = await StorageService.getInt('current_wallet_index');
      if (currentIndex < _wallets.length) {
        _currentWalletIndex = currentIndex;
      }
    } catch (e) {
      _wallets = [];
      _currentWalletIndex = 0;
    }
  }

  /// Save all wallets to storage
  Future<void> _saveWallets() async {
    try {
      final walletsJson = jsonEncode(_wallets.map((wallet) => wallet.toJson()).toList());
      await StorageService.store('all_wallets', walletsJson);
      await StorageService.storeInt('current_wallet_index', _currentWalletIndex);
    } catch (e) {
      throw Exception('Failed to save wallets: $e');
    }
  }

  /// Add a new wallet
  Future<void> addWallet(WalletModel wallet) async {
    _wallets.add(wallet);
    _currentWalletIndex = _wallets.length - 1;
    await _saveWallets();
  }

  /// Switch to a different wallet
  Future<void> switchWallet(int index) async {
    if (index >= 0 && index < _wallets.length) {
      _currentWalletIndex = index;
      await StorageService.storeInt('current_wallet_index', _currentWalletIndex);
    }
  }

  /// Update current wallet
  Future<void> updateCurrentWallet(WalletModel updatedWallet) async {
    if (_wallets.isNotEmpty && _currentWalletIndex < _wallets.length) {
      _wallets[_currentWalletIndex] = updatedWallet;
      await _saveWallets();
    }
  }

  /// Remove a wallet
  Future<void> removeWallet(int index) async {
    if (index >= 0 && index < _wallets.length) {
      _wallets.removeAt(index);
      
      // Adjust current wallet index
      if (_currentWalletIndex >= _wallets.length) {
        _currentWalletIndex = _wallets.length - 1;
      }
      if (_currentWalletIndex < 0) {
        _currentWalletIndex = 0;
      }
      
      await _saveWallets();
    }
  }

  /// Get wallet by address
  WalletModel? getWalletByAddress(String address) {
    try {
      return _wallets.firstWhere((wallet) => wallet.address.toLowerCase() == address.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  /// Check if wallet exists
  bool walletExists(String address) {
    return getWalletByAddress(address) != null;
  }

  /// Get wallet display name
  String getWalletDisplayName(WalletModel wallet) {
    if (wallet.name != null && wallet.name!.isNotEmpty) {
      return wallet.name!;
    }
    return 'Wallet ${wallet.shortAddress}';
  }

  /// Clear all wallets
  Future<void> clearAllWallets() async {
    _wallets.clear();
    _currentWalletIndex = 0;
    await StorageService.delete('all_wallets');
    await StorageService.delete('current_wallet_index');
  }
}

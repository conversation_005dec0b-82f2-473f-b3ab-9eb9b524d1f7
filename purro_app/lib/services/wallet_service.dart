import 'package:web3dart/web3dart.dart';
import '../models/wallet_model.dart';
import '../models/transaction_model.dart';
import '../utils/crypto_utils.dart';
import '../utils/constants.dart';
import 'storage_service.dart';
import 'blockchain_service.dart';

class WalletService {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  final BlockchainService _blockchainService = BlockchainService();
  WalletModel? _currentWallet;
  EthPrivateKey? _privateKey;

  WalletModel? get currentWallet => _currentWallet;
  bool get hasWallet => _currentWallet != null;

  /// Initialize wallet service
  Future<void> initialize() async {
    await _loadWallet();
  }

  /// Create new wallet with mnemonic
  Future<WalletModel> createWallet({String? name}) async {
    try {
      // Generate mnemonic
      final mnemonic = CryptoUtils.generateMnemonic();
      
      // Get address and private key from mnemonic
      final address = await CryptoUtils.getAddressFromMnemonic(mnemonic);
      _privateKey = await CryptoUtils.getPrivateKeyFromMnemonic(mnemonic);
      
      // Create wallet model
      final wallet = WalletModel(
        address: address.hex,
        name: name ?? 'My Wallet',
        balance: BigInt.zero,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // Store wallet data
      await StorageService.storeSeedPhrase(mnemonic);
      await StorageService.storeWalletData(wallet.toJson());
      await StorageService.setWalletCreated(true);

      _currentWallet = wallet;
      
      // Update balance
      await updateBalance();

      return wallet;
    } catch (e) {
      throw Exception('Failed to create wallet: $e');
    }
  }

  /// Import wallet from mnemonic
  Future<WalletModel> importWallet(String mnemonic, {String? name}) async {
    try {
      // Validate mnemonic
      if (!CryptoUtils.validateMnemonic(mnemonic)) {
        throw Exception('Invalid mnemonic phrase');
      }

      // Get address and private key from mnemonic
      final address = await CryptoUtils.getAddressFromMnemonic(mnemonic);
      _privateKey = await CryptoUtils.getPrivateKeyFromMnemonic(mnemonic);
      
      // Create wallet model
      final wallet = WalletModel(
        address: address.hex,
        name: name ?? 'Imported Wallet',
        balance: BigInt.zero,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // Store wallet data
      await StorageService.storeSeedPhrase(mnemonic);
      await StorageService.storeWalletData(wallet.toJson());
      await StorageService.setWalletCreated(true);

      _currentWallet = wallet;
      
      // Update balance
      await updateBalance();

      return wallet;
    } catch (e) {
      throw Exception('Failed to import wallet: $e');
    }
  }

  /// Import wallet from private key
  Future<WalletModel> importWalletFromPrivateKey(String privateKey, {String? name}) async {
    try {
      // Clean private key (remove 0x prefix if present)
      String cleanKey = privateKey.startsWith('0x') ? privateKey.substring(2) : privateKey;

      // Validate private key format
      if (cleanKey.length != 64 || !RegExp(r'^[0-9a-fA-F]+$').hasMatch(cleanKey)) {
        throw Exception('Invalid private key format');
      }

      // Create private key object
      _privateKey = EthPrivateKey.fromHex('0x$cleanKey');
      final address = _privateKey!.address;

      // Create wallet model
      final wallet = WalletModel(
        address: address.hex,
        name: name ?? 'Imported Wallet',
        balance: BigInt.zero,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // Store wallet data
      await StorageService.storeWalletData(wallet.toJson());
      await StorageService.setWalletCreated(true);

      // Store the private key securely
      await StorageService.storeSecurely('private_key', '0x$cleanKey');

      _currentWallet = wallet;

      // Update balance
      await updateBalance();

      return wallet;
    } catch (e) {
      throw Exception('Failed to import wallet from private key: $e');
    }
  }

  /// Load wallet from storage
  Future<void> _loadWallet() async {
    try {
      final walletData = await StorageService.getWalletData();
      if (walletData != null) {
        _currentWallet = WalletModel.fromJson(walletData);
        
        // Load private key from mnemonic or stored private key
        final mnemonic = await StorageService.getSeedPhrase();
        if (mnemonic != null) {
          _privateKey = await CryptoUtils.getPrivateKeyFromMnemonic(mnemonic);
        } else {
          // Try to load from stored private key
          final storedPrivateKey = await StorageService.getSecurely('private_key');
          if (storedPrivateKey != null) {
            _privateKey = EthPrivateKey.fromHex(storedPrivateKey);
          }
        }
      }
    } catch (e) {
      // Handle error silently for now
    }
  }

  /// Update wallet balance
  Future<void> updateBalance() async {
    if (_currentWallet == null) return;

    try {
      final balance = await _blockchainService.getBalance(_currentWallet!.address);
      _currentWallet = _currentWallet!.copyWith(
        balance: balance,
        lastUpdated: DateTime.now(),
      );
      
      // Save updated wallet
      await StorageService.storeWalletData(_currentWallet!.toJson());
    } catch (e) {
      throw Exception('Failed to update balance: $e');
    }
  }

  /// Send transaction
  Future<String> sendTransaction({
    required String to,
    required double amount,
    double? gasPrice,
    int? gasLimit,
  }) async {
    if (_currentWallet == null || _privateKey == null) {
      throw Exception('No wallet available');
    }

    try {
      final value = CryptoUtils.ethToWei(amount);
      final gasPriceWei = gasPrice != null 
          ? CryptoUtils.ethToWei(gasPrice) 
          : await _blockchainService.getGasPrice();

      final txHash = await _blockchainService.sendTransaction(
        credentials: _privateKey!,
        to: to,
        value: value,
        gasPrice: gasPriceWei,
        gasLimit: gasLimit,
      );

      // Update balance after transaction
      await updateBalance();

      return txHash;
    } catch (e) {
      throw Exception('Failed to send transaction: $e');
    }
  }

  /// Get transaction history
  Future<List<TransactionModel>> getTransactionHistory() async {
    if (_currentWallet == null) return [];

    try {
      return await _blockchainService.getTransactionHistory(_currentWallet!.address);
    } catch (e) {
      throw Exception('Failed to get transaction history: $e');
    }
  }

  /// Get current gas price
  Future<BigInt> getCurrentGasPrice() async {
    return await _blockchainService.getGasPrice();
  }

  /// Estimate transaction fee
  Future<BigInt> estimateTransactionFee({
    required String to,
    required double amount,
  }) async {
    if (_currentWallet == null) {
      throw Exception('No wallet available');
    }

    try {
      final value = CryptoUtils.ethToWei(amount);
      final gasEstimate = await _blockchainService.estimateGas(
        from: _currentWallet!.address,
        to: to,
        value: value,
      );
      
      final gasPrice = await _blockchainService.getGasPrice();
      return gasEstimate * gasPrice;
    } catch (e) {
      throw Exception('Failed to estimate transaction fee: $e');
    }
  }

  /// Check if address is valid
  bool isValidAddress(String address) {
    return _blockchainService.isValidAddress(address);
  }

  /// Get network info
  Map<String, dynamic> getNetworkInfo() {
    return _blockchainService.getNetworkInfo();
  }

  /// Check network connectivity
  Future<bool> isNetworkConnected() async {
    return await _blockchainService.isNetworkConnected();
  }

  /// Delete wallet
  Future<void> deleteWallet() async {
    try {
      await StorageService.deleteSeedPhrase();
      await StorageService.delete(AppConstants.walletKey);
      await StorageService.setWalletCreated(false);
      
      _currentWallet = null;
      _privateKey = null;
    } catch (e) {
      throw Exception('Failed to delete wallet: $e');
    }
  }

  /// Get seed phrase
  Future<String?> getSeedPhrase() async {
    return await StorageService.getSeedPhrase();
  }

  /// Dispose resources
  void dispose() {
    _blockchainService.dispose();
  }
}

class AppConstants {
  // Network Configuration
  static const String rpcUrl = 'https://rpc.hyperliquid.xyz/evm';
  static const int chainId = 999;
  static const String currencySymbol = 'HYPE';
  static const String explorerUrl = 'https://www.hyperscan.com';
  
  // App Configuration
  static const String appName = 'Purro Wallet';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String walletKey = 'wallet_data';
  static const String seedPhraseKey = 'seed_phrase';
  static const String isWalletCreatedKey = 'is_wallet_created';
  static const String transactionHistoryKey = 'transaction_history';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultRadius = 12.0;
  static const int seedPhraseLength = 12;
  
  // Colors
  static const int primaryColorValue = 0xFF6366F1;
  static const int secondaryColorValue = 0xFF8B5CF6;
  static const int successColorValue = 0xFF10B981;
  static const int errorColorValue = 0xFFEF4444;
  static const int warningColorValue = 0xFFF59E0B;
}

class NetworkConstants {
  static const String hyperliquidName = 'Hyperliquid';
  static const String hyperliquidRpc = AppConstants.rpcUrl;
  static const int hyperliquidChainId = AppConstants.chainId;
  static const String hyperliquidSymbol = AppConstants.currencySymbol;
  static const String hyperliquidExplorer = AppConstants.explorerUrl;
}

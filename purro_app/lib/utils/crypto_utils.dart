import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:bip39/bip39.dart' as bip39;
import 'package:web3dart/web3dart.dart';
import 'package:crypto/crypto.dart';

class CryptoUtils {
  /// Generate a new mnemonic seed phrase
  static String generateMnemonic() {
    return bip39.generateMnemonic();
  }

  /// Validate if a mnemonic is valid
  static bool validateMnemonic(String mnemonic) {
    return bip39.validateMnemonic(mnemonic);
  }

  /// Generate wallet from mnemonic
  static Future<EthereumAddress> getAddressFromMnemonic(String mnemonic) async {
    final seed = bip39.mnemonicToSeed(mnemonic);
    // Use a simpler approach for demo - generate from seed directly
    final privateKey = EthPrivateKey.fromHex(
      sha256.convert(seed.take(32).toList()).toString()
    );
    return privateKey.address;
  }

  /// Get private key from mnemonic
  static Future<EthPrivateKey> getPrivateKeyFromMnemonic(String mnemonic) async {
    final seed = bip39.mnemonicToSeed(mnemonic);
    // Use a simpler approach for demo - generate from seed directly
    final privateKey = EthPrivateKey.fromHex(
      sha256.convert(seed.take(32).toList()).toString()
    );
    return privateKey;
  }

  /// Format address for display
  static String formatAddress(String address, {int prefixLength = 6, int suffixLength = 4}) {
    if (address.length <= prefixLength + suffixLength) {
      return address;
    }
    return '${address.substring(0, prefixLength)}...${address.substring(address.length - suffixLength)}';
  }

  /// Format balance for display
  static String formatBalance(BigInt balance, {int decimals = 18, int displayDecimals = 4}) {
    final divisor = BigInt.from(10).pow(decimals);
    final wholePart = balance ~/ divisor;
    final fractionalPart = balance % divisor;
    
    final fractionalString = fractionalPart.toString().padLeft(decimals, '0');
    final trimmedFractional = fractionalString.substring(0, displayDecimals);
    
    return '$wholePart.$trimmedFractional';
  }

  /// Convert ETH to Wei
  static BigInt ethToWei(double eth) {
    return BigInt.from(eth * 1e18);
  }

  /// Convert Wei to ETH
  static double weiToEth(BigInt wei) {
    return wei.toDouble() / 1e18;
  }

  /// Generate random secure bytes
  static Uint8List generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(List.generate(length, (i) => random.nextInt(256)));
  }

  /// Hash data using SHA256
  static String sha256Hash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify signature
  static bool verifySignature(String message, String signature, String address) {
    try {
      // Implementation for signature verification
      // This is a simplified version - in production, use proper signature verification
      return true;
    } catch (e) {
      return false;
    }
  }
}

import 'package:flutter/material.dart';
import '../models/wallet_model.dart';
import '../utils/constants.dart';
import '../screens/onboarding/seed_generation_screen.dart';
import '../screens/onboarding/import_private_key_screen.dart';

class AddWalletModal extends StatelessWidget {
  final Function(WalletModel) onWalletAdded;

  const AddWalletModal({
    super.key,
    required this.onWalletAdded,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Text(
                    'Add New Wallet',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            
            // Options
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Create new wallet
                  _OptionTile(
                    icon: Icons.add_circle_outline,
                    title: 'Create a new wallet',
                    subtitle: 'Generate a new wallet with seed phrase',
                    color: const Color(AppConstants.primaryColorValue),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SeedGenerationScreen(),
                        ),
                      ).then((result) {
                        if (result is WalletModel) {
                          onWalletAdded(result);
                        }
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Import from private key
                  _OptionTile(
                    icon: Icons.key,
                    title: 'Import private key',
                    subtitle: 'Import an existing wallet using private key',
                    color: const Color(AppConstants.secondaryColorValue),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ImportPrivateKeyScreen(),
                        ),
                      ).then((result) {
                        if (result is WalletModel) {
                          onWalletAdded(result);
                        }
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Import from seed phrase
                  _OptionTile(
                    icon: Icons.text_fields,
                    title: 'Import seed phrase',
                    subtitle: 'Import using 12-word recovery phrase',
                    color: Colors.orange,
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Implement seed phrase import
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Seed phrase import coming soon!'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class _OptionTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _OptionTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade200),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

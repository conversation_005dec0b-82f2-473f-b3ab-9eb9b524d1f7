import 'package:flutter/material.dart';
import '../models/wallet_model.dart';
import '../services/wallet_manager_service.dart';
import '../utils/constants.dart';
import '../utils/crypto_utils.dart';
import 'add_wallet_modal.dart';

class WalletSelectorModal extends StatefulWidget {
  final Function(WalletModel) onWalletSelected;

  const WalletSelectorModal({
    super.key,
    required this.onWalletSelected,
  });

  @override
  State<WalletSelectorModal> createState() => _WalletSelectorModalState();
}

class _WalletSelectorModalState extends State<WalletSelectorModal> {
  final WalletManagerService _walletManager = WalletManagerService();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Select Wallet',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          
          // Wallets list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _walletManager.wallets.length,
              itemBuilder: (context, index) {
                final wallet = _walletManager.wallets[index];
                final isSelected = index == _walletManager.currentWalletIndex;
                
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? const Color(AppConstants.primaryColorValue).withOpacity(0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected 
                          ? const Color(AppConstants.primaryColorValue)
                          : Colors.grey.shade200,
                    ),
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(AppConstants.primaryColorValue),
                            Color(AppConstants.secondaryColorValue),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Center(
                        child: Text(
                          _getWalletInitials(wallet),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    title: Text(
                      _walletManager.getWalletDisplayName(wallet),
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          wallet.shortAddress,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${CryptoUtils.formatBalance(wallet.balance)} ${AppConstants.currencySymbol}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    trailing: isSelected 
                        ? Icon(
                            Icons.check_circle,
                            color: const Color(AppConstants.primaryColorValue),
                          )
                        : null,
                    onTap: () async {
                      await _walletManager.switchWallet(index);
                      widget.onWalletSelected(wallet);
                      if (mounted) {
                        Navigator.pop(context);
                      }
                    },
                  ),
                );
              },
            ),
          ),
          
          // Add wallet button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                _showAddWalletModal();
              },
              icon: const Icon(Icons.add),
              label: const Text('Add a new wallet'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(AppConstants.primaryColorValue),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: const BorderSide(
                  color: Color(AppConstants.primaryColorValue),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getWalletInitials(WalletModel wallet) {
    if (wallet.name != null && wallet.name!.isNotEmpty) {
      final words = wallet.name!.split(' ');
      if (words.length >= 2) {
        return '${words[0][0]}${words[1][0]}'.toUpperCase();
      }
      return wallet.name![0].toUpperCase();
    }
    return wallet.address.substring(2, 4).toUpperCase();
  }

  void _showAddWalletModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddWalletModal(
        onWalletAdded: (wallet) {
          setState(() {});
          widget.onWalletSelected(wallet);
        },
      ),
    );
  }
}
